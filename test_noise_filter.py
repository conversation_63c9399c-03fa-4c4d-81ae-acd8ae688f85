#!/usr/bin/env python3
"""
测试噪声过滤功能的简单脚本
"""

import sys
import os
import time
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from component.data_preprocessing import DataPreprocessor

def create_test_data():
    """创建包含异常值的测试数据"""
    base_time = int(time.time()) - 7 * 24 * 3600  # 7天前开始
    test_data = []
    
    for i in range(1000):  # 1000个数据点
        timestamp = base_time + i * 600  # 每10分钟一个数据点
        # 基础值：正弦波 + 噪声
        value = 100 + 20 * np.sin(i * 0.01) + np.random.normal(0, 2)
        
        # 添加一些异常值
        if i in [100, 200, 300, 400, 500]:
            value += 50  # 异常高值
        elif i in [150, 250, 350, 450, 550]:
            value -= 40  # 异常低值
            
        test_data.append({"timestamp": timestamp, "value": value})
    
    return test_data

def test_noise_filter():
    """测试噪声过滤功能"""
    print("创建测试数据...")
    test_data = create_test_data()
    print(f"原始数据点数: {len(test_data)}")
    
    print("\n测试噪声过滤功能...")
    preprocessor = DataPreprocessor()
    
    # 测试Z-score方法
    filtered_data, outlier_timestamps = preprocessor.filter_noise(
        test_data, 
        method="zscore", 
        threshold=3.0
    )
    
    print(f"Z-score过滤后数据点数: {len(filtered_data)}")
    print(f"检测到的异常点数: {len(outlier_timestamps)}")
    print(f"异常点时间戳: {outlier_timestamps[:5]}...")  # 只显示前5个
    
    # 测试IQR方法
    filtered_data_iqr, outlier_timestamps_iqr = preprocessor.filter_noise(
        test_data, 
        method="iqr", 
        threshold=1.5
    )
    
    print(f"\nIQR过滤后数据点数: {len(filtered_data_iqr)}")
    print(f"IQR检测到的异常点数: {len(outlier_timestamps_iqr)}")
    
    return True

if __name__ == "__main__":
    try:
        test_noise_filter()
        print("\n✅ 噪声过滤功能测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
