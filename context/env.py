import os
import bytedenv

is_ppe_env: bool = os.environ.get('TCE_HOST_ENV') == "ppe"
is_prod_env: bool = os.environ.get('TCE_HOST_ENV') == 'online' or os.environ.get('TCE_HOST_ENV') == "ppe"
is_boe_env: bool = os.environ.get('TCE_HOST_ENV') == 'boe'
is_sg_env: bool = os.environ.get('TCE_ZONE') == 'Aliyun_SG'
is_ttp_env: bool = os.environ.get('TCE_ZONE') == 'US-TTP'
is_gcp_env: bool = bytedenv.get_idc_name() == 'useast2a' or os.environ.get('TCE_ZONE') == 'US-East-Red' or bytedenv.get_idc_name() == 'no1a'
is_restricted_env: bool = is_gcp_env or is_ttp_env  # 受限机房
local_dps_token: str = os.environ.get('LOCAL_DPS_TOKEN', '')
cluster_name = os.environ.get('CLUSTER_NAME', "default")
oai_ak = os.environ.get('OAI_AK', '')

is_extreme_disaster_cluster: bool = os.environ.get("EXTREME_DISASTER_CLUSTER", "false") == "true"

if is_prod_env:
    os.environ.setdefault('TZ', 'Asia/Shanghai')
