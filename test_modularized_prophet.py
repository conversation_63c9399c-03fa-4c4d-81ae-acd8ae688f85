#!/usr/bin/env python3
"""
测试模块化后的Prophet异常检测函数
"""

import sys
import os
import time
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from component.prophet_detection import (
    train_prophet_model_full,
    use_pretrained_prophet_model,
    calculate_thresholds_and_detect_anomalies,
    detect_anomalies_with_thresholds,
    create_debug_plots
)

def create_test_data():
    """创建测试用的历史数据和今日数据"""
    base_time = int(time.time()) - 7 * 24 * 3600  # 7天前开始
    
    # 创建历史数据（7天）
    history_data = []
    for i in range(300):  # 300个数据点
        timestamp = base_time + i * 1800  # 每30分钟一个数据点
        # 基础值：正弦波 + 噪声
        value = 100 + 10 * np.sin(i * 0.05) + np.random.normal(0, 2)
        
        # 添加一些异常值
        if i in [50, 51]:  # 连续异常值
            value += 25  # 异常高值
        elif i in [150]:  # 单个异常值
            value -= 20  # 异常低值
        elif i in [250]:  # 单个异常值
            value += 22  # 异常高值
            
        history_data.append({"timestamp": timestamp, "value": value})
    
    # 创建今日数据（1天）
    today_start_time = base_time + 300 * 1800
    today_data = []
    for i in range(48):  # 48个数据点（1天，每30分钟一个点）
        timestamp = today_start_time + i * 1800
        # 基础值：正弦波 + 噪声
        value = 100 + 10 * np.sin((300 + i) * 0.05) + np.random.normal(0, 2)
        
        # 添加一些今日异常值
        if i in [20, 21]:  # 连续异常值
            value += 30  # 异常高值
        elif i in [35]:  # 单个异常值
            value -= 25  # 异常低值
            
        today_data.append({"timestamp": timestamp, "value": value})
    
    return history_data, today_data

def test_modularized_functions():
    """测试模块化后的各个函数"""
    print("创建测试数据...")
    history_data, today_data = create_test_data()
    print(f"历史数据点数: {len(history_data)}")
    print(f"今日数据点数: {len(today_data)}")
    
    # 测试模块1: 训练和分解部分
    print("\n测试模块1: 训练和分解部分...")
    start_time = datetime.now()
    
    model, df_history_smoothed, trend, daily, resid, outlier_timestamps, noise_filter_plot = train_prophet_model_full(
        history_data, debug_other_image=True
    )
    
    print(f"训练完成:")
    print(f"- 处理后历史数据点数: {len(df_history_smoothed)}")
    print(f"- 异常值数量: {len(outlier_timestamps)}")
    print(f"- 趋势分量长度: {len(trend)}")
    print(f"- 日周期分量长度: {len(daily)}")
    print(f"- 残差分量长度: {len(resid)}")
    print(f"- 噪声过滤图表长度: {len(noise_filter_plot)} 字符")
    
    # 保存噪声过滤图表
    if noise_filter_plot:
        with open("modularized_noise_filter.html", "w", encoding="utf-8") as f:
            f.write(noise_filter_plot)
        print("噪声过滤图表已保存为 modularized_noise_filter.html")
    
    # 测试模块2: 阈值计算和异常检测
    print("\n测试模块2: 阈值计算和异常检测...")
    
    # 准备今日数据
    today_times = [datetime.fromtimestamp(item["timestamp"]) for item in today_data]
    today_values = np.array([item["value"] for item in today_data])
    from component.prophet_detection import smooth_series
    today_values_smoothed = np.array(smooth_series(today_values, window=3, method="mean"))
    
    # 计算今日预测
    from component.prophet_detection import prophet_forecast
    trend_pred_values = prophet_forecast(model, today_times, component="trend")
    daily_pred_values = prophet_forecast(model, today_times, component="daily")
    today_resid = today_values_smoothed - (trend_pred_values + daily_pred_values)
    
    # 计算阈值
    threshold_data = calculate_thresholds_and_detect_anomalies(
        today_times, today_values_smoothed, df_history_smoothed, resid,
        min_anomaly_run=4, sigma_soft=3, sigma_hard=5, sigma_diff=5
    )
    
    # 进行异常检测
    is_anomaly, anomaly_times, final_anomaly_idx = detect_anomalies_with_thresholds(
        today_times, today_resid, threshold_data, min_anomaly_run=4
    )
    
    print(f"异常检测完成:")
    print(f"- 是否有异常: {is_anomaly}")
    print(f"- 异常时间点数量: {len(anomaly_times)}")
    print(f"- 异常索引: {final_anomaly_idx}")
    
    return True

if __name__ == "__main__":
    try:
        success = test_modularized_functions()
        if success:
            print("\n✅ 模块化函数测试通过！")
            print("各个模块都能正常工作：")
            print("- 模块1: 训练和分解部分（包括历史数据分量获取）")
            print("- 模块2: 阈值计算和异常检测部分")
            print("- 模块3: 绘图部分（暂未测试，需要完整实现）")
        else:
            print("\n❌ 测试失败")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
