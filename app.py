from io import BytesIO
import json
import os
import time
import bytedtos
import bytedunicorn
import concurrent
from concurrent.futures import ThreadPoolExecutor, as_completed
import flask
import bytedlogger
import logging
import traceback
from datetime import datetime, timezone, timedelta
from component.prophet_detection import prophet_anomaly_detection
from component.data_center import query_metrics
from component.tos_update import update_tos
from component.tcc_client import get_tcc, update_tcc, get_metrics_query_list
from component.tos_client import tos_client
from context.env import is_prod_env
from component.tcc_client import model_tag_names

# 设置固定时区为UTC，避免时区变化导致的问题
os.environ['TZ'] = 'UTC'
time.tzset()

app = flask.Flask(__name__)
bytedunicorn.init_flask(app)
bytedlogger.config_default()

def try_get_pretrained_model(client, vregion, metric_query, start_time, dims):
    """
    尝试获取预训练的Prophet模型

    Args:
        client: TOS客户端
        vregion: 区域
        metric_query: 指标查询字符串
        start_time: 开始时间
        dims: 维度信息（用于日志）

    Returns:
        tuple: (model_data, last_training_timestamp)
               如果找不到模型则返回 (None, None)
    """
    # 计算最近整点的时间戳，并转换为yyyy-MM-dd_HH格式
    current_hour_timestamp = (start_time // 3600) * 3600
    previous_hour_timestamp = current_hour_timestamp - 3600

    # 转换为字符串格式
    current_hour_str = datetime.fromtimestamp(current_hour_timestamp).strftime("%Y_%m_%d_%H")
    previous_hour_str = datetime.fromtimestamp(previous_hour_timestamp).strftime("%Y_%m_%d_%H")

    # 尝试获取最近整点的模型
    for timestamp_str in [current_hour_str, previous_hour_str]:
        try:
            model_key = f"anomaly_detection_metrics/models/{vregion}/{metric_query}/{timestamp_str}"
            resp = client.get_object(key=model_key)
            model_data = json.loads(resp.data.decode("utf-8"))
            logging.info(f"维度{dims}找到预训练模型: {model_key}, 模型训练数据结束时间戳: {model_data.get('last_training_timestamp')}")
            return model_data, model_data.get("last_training_timestamp")
        except bytedtos.TosException as e:
            if e.code == 404:
                continue  # 尝试下一个时间戳
            else:
                logging.error(f"获取模型文件异常: {model_key}, {str(e)}")
                break
        except Exception as e:
            logging.error(f"获取模型文件异常: {model_key}, {str(e)}")
            break

    logging.info(f"维度{dims}未找到预训练模型: {metric_query}")
    return None, None

def _fetch_model_data(client, vregion, metric_query, start_time, dims):
    """
    获取预训练模型数据的独立函数
    """
    return try_get_pretrained_model(client, vregion, metric_query, start_time, dims)

def _fetch_history_data(client, vregion, metric_query, start_time, dims):
    """
    获取历史数据的独立函数
    """
    seven_days = 7 * 24 * 3600
    min_timestamp = start_time - seven_days
    two_days_ago_start = datetime.fromtimestamp(start_time) - timedelta(days=2)
    history_values = []

    # 获取历史数据
    try:
        resp = client.get_object(key=f"anomaly_detection_metrics/{vregion}/{metric_query}")
        tos_data = json.loads(resp.data.decode("utf-8"))
        logging.info(f"维度{dims}查询到TOS历史数据: {metric_query}")

        # 获取TOS中的历史数据
        history_values = [
            v for v in tos_data[0]["values"] if min_timestamp <= v["timestamp"] < start_time
        ]

        # 检查是否需要补全数据
        last_ts = history_values[-1]["timestamp"] if history_values else None
        if last_ts is not None and last_ts < start_time - 30:
            logging.info(f"维度{dims}需要补全历史数据: {metric_query}")
            patch_result = query_metrics(
                start_time=last_ts + 30,
                end_time=start_time,
                vregion=vregion,
                query=metric_query,
                block_cache=False,
            )
            if patch_result and patch_result[0].get("values"):
                patch_values = [v for v in patch_result[0]["values"] if v["timestamp"] > last_ts and v["timestamp"] < start_time]
                history_values.extend(patch_values)

    except bytedtos.TosException as e:
        if e.code == 404:
            logging.info(f"TOS中不存在历史数据: {metric_query}，直接查询数据中心")
            # TOS中没有数据，直接查询数据中心
            history_result = query_metrics(
                start_time=min_timestamp,
                end_time=start_time,
                vregion=vregion,
                query=metric_query,
                block_cache=False,
                log=True
            )
            history_values = []
            if history_result and history_result[0].get("values"):
                history_values = [v for v in history_result[0]["values"] if min_timestamp <= v["timestamp"] < start_time]
        else:
            logging.info(f"查询TOS历史数据异常: {metric_query}, {str(e)}")

    # 检查历史数据充足性：确保有至少2天前的同时期起始数据
    history_timestamps = [item["timestamp"] for item in history_values]
    is_sufficient = history_timestamps and min(history_timestamps) <= two_days_ago_start.timestamp()

    return history_values, is_sufficient

def query_history_data(client, vregion, metric_query, start_time, dims):
    """
    查询历史数据的通用方法（并行版本）
    获取数据和获取模型部分并行进行

    Args:
        client: TOS客户端
        vregion: 区域
        metric_query: 指标查询字符串
        start_time: 开始时间
        dims: 维度信息（用于日志）

    Returns:
        tuple: (history_values, is_sufficient, model_data, last_training_timestamp)
               history_values: 历史数据列表
               is_sufficient: 数据是否充足（至少2天前的同时期数据）
               model_data: 预训练的模型数据（如果存在）
               last_training_timestamp: 模型训练数据的最后时间戳（如果存在）
    """
    # 使用线程池并行执行获取模型和获取历史数据
    with ThreadPoolExecutor(max_workers=2) as executor:
        # 提交两个任务
        model_future = executor.submit(_fetch_model_data, client, vregion, metric_query, start_time, dims)
        history_future = executor.submit(_fetch_history_data, client, vregion, metric_query, start_time, dims)

        # 等待两个任务完成并获取结果
        model_data, last_training_timestamp = model_future.result()
        history_values, is_sufficient = history_future.result()

    return history_values, is_sufficient, model_data, last_training_timestamp

@app.route("/anomaly_detection", methods=["POST"])
def anomaly_detection_handler():
    logging.info("当前环境: 生产环境" if is_prod_env else "当前环境: 非生产环境")
    # 输出当前机器时区和时间
    current_time = datetime.now(timezone.utc)
    logging.info(f"当前UTC时间: {current_time.isoformat()}")
    logging.info(f"当前时间戳: {int(current_time.timestamp())}")
    logging.info(f"当前时区设置: {os.environ.get('TZ', 'Not set')}")
    api_start_time = time.time()
    try:
        data = flask.request.get_json()
        start_time = data.get("start_time")
        end_time = data.get("end_time")
        vregion = data.get("vregion")
        query = data.get("query")
        block_cache = data.get("block_cache", False)
        debug_main_image = data.get("debug_main_image", False)
        debug_all_image = data.get("debug_all_image", False)
        debug_resid_image = data.get("debug_resid_image", False)
        debug_other_image = data.get("debug_other_image", False)
        logging.info(f"当前异常检测vregion: {vregion}, query: {query}")

        logging.info(f"开始查询当天检测数据")
        query_today_start_time = time.time()
        merged_data = query_metrics(start_time, end_time, vregion, query, block_cache)
        
        if not merged_data:
            logging.info("没有查询到当天检测数据，返回空结果")
            return {"code": 5, "msg": "没有查询到当天检测数据"}
        query_today_end_time = time.time()
        logging.info(f"当天检测数据查询完成, 耗时: {query_today_end_time - query_today_start_time:.2f}秒")

        results = []
        client = tos_client.client
        if not client:
            logging.error("获取TOS客户端失败")
            return {"code": 5, "msg": "获取TOS客户端失败"}
        
        # 标记是否已添加到TCC更新列表
        # added_to_tos_update = False

        # 从TCC动态获取查询语句列表，检查当前query是否在列表中
        # existing_metrics = set()
        # try:
        #     query_list = get_metrics_query_list()
        #     if query_list:
        #         # 固定的vregion数组（与tos_update.py保持一致）
        #         vregion_list = [
        #             "US-East",
        #             "Singapore-Central", 
        #             "US-EastRed",
        #             "EU-TTP2",
        #             "US-TTP",
        #             "US-TTP2"
        #         ]
                
        #         # 构建已存在的metrics组合
        #         for query_item in query_list:
        #             for vr in vregion_list:
        #                 existing_metrics.add(f"{vr} {query_item}")
                        
        #         logging.info(f"从TCC获取到 {len(query_list)} 个查询语句和 {len(vregion_list)} 个vregion")
        #     else:
        #         logging.info("未从TCC获取到查询语句列表")
        # except Exception as e:
        #     logging.error(f"从TCC获取查询语句列表失败: {e}")

        for md in merged_data:
            # 拼接具体metrics query字符串
            dims = md.get("dimensions", {})

            # 构造具体的metric_query
            dims_str = "{" + ",".join([f"{k}={v}" for k, v in sorted(dims.items())]) + "}"

            # 找到第一个{和第一个}来构造完整query
            first_l = query.find("{")
            first_r = query.find("}")
            prefix_metric = query[:first_l]  # 第一个{之前作为前缀
            suffix_metric = query[first_r + 1:]  # 第一个}之后作为后缀
            metric_query = f"{prefix_metric}{dims_str}{suffix_metric}"

            # 若dims中某一个k在model_tag_names里，则单独构造一个不包含该k、v的metric_query_no_model_tag
            metric_query_no_model_tag = None
            for k in dims.keys():
                if k in model_tag_names:
                    # 构造不包含该model tag的dims
                    dims_no_model_tag = {key: value for key, value in dims.items() if key != k}
                    dims_no_model_tag_str = "{" + ",".join([f"{key}={value}" for key, value in sorted(dims_no_model_tag.items())]) + "}"
                    metric_query_no_model_tag = f"{prefix_metric}{dims_no_model_tag_str}{suffix_metric}"
                    break  # 只处理第一个找到的model tag

            metric_line = f"{vregion} {query}"

            today_values = md.get("values", [])

            # 使用新的查询逻辑：先用原本的metric_query进行查询
            try:
                query_start_time = time.time()
                history_values, is_sufficient, model_data, last_training_timestamp = query_history_data(client, vregion, metric_query, start_time, dims)

                # 如果数据不足且有备用查询，则使用metric_query_no_model_tag
                if not is_sufficient and metric_query_no_model_tag:
                    logging.info(f"维度{dims}原始查询数据不足，尝试使用备用查询: {metric_query_no_model_tag}")
                    history_values, is_sufficient, model_data, last_training_timestamp = query_history_data(client, vregion, metric_query_no_model_tag, start_time, dims)
                
                query_end_time = time.time()
                logging.info(f"查询历史模型和数据耗时: {query_end_time - query_start_time:.2f}秒")
                
                # 如果数据仍然不足，返回数据不足的结果
                if not is_sufficient:
                    two_days_ago_start = datetime.fromtimestamp(start_time) - timedelta(days=2)
                    logging.warning(f"维度{dims}历史数据过少，缺少2天前({two_days_ago_start})的同时期数据，暂不支持异常检测")
                    results.append({
                        "dimensions": dims,
                        "status": "insufficient_data",
                        "message": "历史同期数据少于2天，暂不支持异常检测",
                        "is_anomaly": False,
                        "anomaly_times": [],
                        "img_main": "",
                        "img_all_data": "",
                        "img_resid_data": "",
                        "img_details": {}
                    })
                    continue

                # 检查是否在动态查询列表中（如未存在且其他维度未添加过）
                # if metric_line not in existing_metrics and not added_to_tos_update:
                #     added_to_tos_update = True
                #     logging.info(f"新指标不在动态查询列表中，但已自动处理TOS数据: {vregion} {query}")

                # 执行异常检测
                is_anomaly, anomaly_times, img_main, img_all_data, img_resid_data, imgs_dict = prophet_anomaly_detection(
                    history_values, today_values, start_time, debug_main_image, debug_all_image, debug_other_image, debug_resid_image,
                    model_data=model_data, last_training_timestamp=last_training_timestamp
                )
                results.append({
                    "dimensions": dims,
                    "status": "success",
                    "is_anomaly": is_anomaly,
                    "anomaly_times": anomaly_times,
                    "img_main": img_main,
                    "img_all_data": img_all_data,
                    "img_resid_data": img_resid_data,
                    "img_details": imgs_dict
                })
                logging.info(f"维度{dims}异常检测完成")
            except Exception as e:
                logging.error(f"维度{dims}异常检测失败: {traceback.format_exc()}")
                return {"code": 5, "msg": str(e), "stacktrace": traceback.format_exc()}

        api_end_time = time.time()
        logging.info(f"异常检测接口完成，耗时: {api_end_time - api_start_time:.2f} 秒")
        return {
            "code": 0,
            "results": results
        }
    except Exception as e:
        logging.error(f"An error occurred: {traceback.format_exc()}")
        return {"code": 5, "msg": str(e), "stacktrace": traceback.format_exc()}

if __name__ == "__main__":
    app.run("localhost", "8000", threaded=True)
    logging.info("当前环境: 生产环境" if is_prod_env else "当前环境: 非生产环境")
