import pytest
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
import numpy as np
from prophet import Prophet
from component.prophet_detection import smooth_series, prophet_decompose, prophet_forecast, prophet_anomaly_detection
import logging
from datetime import datetime, timedelta

@pytest.mark.parametrize(
    "values, window, method, expected",
    [
        # Test normal path with mean method
        ([1, 2, 3, 4, 5], 3, "mean", [1.5, 2.0, 3.0, 4.0, 4.5]),
        # Test normal path with median method
        ([1, 2, 3, 4, 5], 3, "median", [1.5, 2.0, 3.0, 4.0, 4.5]),
        # Test edge case with single value
        ([1], 3, "mean", [1.0]),
        # Test edge case with window larger than series length
        ([1, 2], 5, "median", [1.0, 1.5]),
    ],
)
def test_smooth_series_normal(values, window, method, expected):
    """Test smooth_series with valid inputs."""
    result = smooth_series(values, window=window, method=method)
    assert result == expected

@pytest.mark.parametrize(
    "values, window, method, error",
    [
        # Test invalid method
        ([1, 2, 3], 3, "invalid", ValueError),
        # Test invalid window type
        ([1, 2, 3], "invalid", "mean", TypeError),
    ],
)
def test_smooth_series_exceptions(values, window, method, error):
    """Test smooth_series with invalid inputs that should raise exceptions."""
    with pytest.raises(error):
        smooth_series(values, window=window, method=method)
        
@pytest.fixture
def mock_series():
    """Fixture providing a mock time series for testing"""
    dates = pd.date_range(start='2023-01-01', periods=10, freq='D')
    values = np.random.rand(10)
    return pd.Series(values, index=dates)

@pytest.fixture
def mock_prophet():
    """Fixture providing a mock Prophet model"""
    with patch('component.prophet_detection.Prophet') as mock:
        prophet_instance = mock.return_value
        prophet_instance.fit.return_value = prophet_instance
        forecast_data = {
            'trend': np.linspace(0.1, 1.0, 10),
            'daily': np.linspace(0.001, 0.01, 10)
        }
        prophet_instance.predict.return_value = pd.DataFrame(forecast_data)
        yield mock

def test_prophet_decompose_normal(mock_series, mock_prophet):
    """Test normal path of prophet_decompose"""
    with mock_prophet:
        model = prophet_decompose(mock_series)

        # Verify the return type
        assert isinstance(model, type(mock_prophet.return_value))

        # Verify Prophet was called with expected parameters
        mock_prophet.assert_called_once_with(
            yearly_seasonality=False,
            weekly_seasonality=False,
            daily_seasonality=True,
            interval_width=0.9,
            changepoint_prior_scale=0.001,
            n_changepoints=0,
            growth='linear',
            seasonality_prior_scale=10.0,
        )

def test_prophet_decompose_empty_series():
    """Test with empty input series"""
    empty_series = pd.Series([], dtype=float)
    with pytest.raises(ValueError):
        prophet_decompose(empty_series)

def test_prophet_decompose_invalid_freq(mock_series):
    """Test with invalid frequency parameter"""
    with pytest.raises(ValueError):
        prophet_decompose(mock_series, freq="INVALID")

@pytest.fixture
def mock_model():
    """Fixture for creating a mock Prophet model"""
    model = Mock()
    forecast_data = {
        "yhat": np.array([10.5, 11.2, 12.8]),
        "yhat_lower": np.array([9.8, 10.5, 11.9]),
        "yhat_upper": np.array([11.2, 12.0, 13.7])
    }
    model.predict.return_value = pd.DataFrame(forecast_data)
    return model

@pytest.fixture
def future_index():
    """Fixture for creating future dates"""
    return pd.date_range(start="2023-01-01", periods=3)

def test_prophet_forecast_normal_case(mock_model, future_index):
    """Test normal case with valid inputs"""
    result = prophet_forecast(mock_model, future_index, "yhat")
    assert isinstance(result, np.ndarray)
    assert len(result) == 3
    assert result.tolist() == [10.5, 11.2, 12.8]
    mock_model.predict.assert_called_once()

def test_prophet_forecast_different_component(mock_model, future_index):
    """Test with different forecast component"""
    result = prophet_forecast(mock_model, future_index, "yhat_upper")
    assert isinstance(result, np.ndarray)
    assert len(result) == 3
    assert result.tolist() == [11.2, 12.0, 13.7]

def test_prophet_forecast_invalid_component(mock_model, future_index):
    """Test with invalid component name"""
    with pytest.raises(KeyError):
        prophet_forecast(mock_model, future_index, "invalid_component")
    mock_model.predict.assert_called_once()

@pytest.fixture
def mock_data():
    # 创建测试数据
    base_time = int(datetime(2023, 1, 1).timestamp())
    all_history = [
        {"timestamp": base_time + i*3600, "value": 10 + np.sin(i/24)*2 + np.random.normal(0, 0.1)}
        for i in range(24*14)  # 14天的数据
    ]
    today = [
        {"timestamp": base_time + 24*14*3600 + i*3600, "value": 12 + np.sin(i/24)*2 + np.random.normal(0, 0.5)}
        for i in range(24)  # 1天的数据
    ]
    # 添加一些异常点
    today[5]["value"] = 20
    today[10]["value"] = 5
    today[15]["value"] = 25
    
    return {
        "all_history": all_history,
        "today": today,
        "start_time": base_time
    }

@pytest.fixture
def mock_prophet():
    # Mock Prophet相关方法
    with patch('prophet.Prophet') as mock_prophet:
        model = MagicMock(spec=Prophet)
        mock_prophet.return_value = model
        
        # Mock预测结果
        forecast = MagicMock()
        forecast.__getitem__.side_effect = lambda x: pd.DataFrame({
            "trend": np.linspace(10, 12, 24),
            "daily": np.cos(np.linspace(0, 2*np.pi, 24)),
            "yhat": np.linspace(10, 12, 24) + np.cos(np.linspace(0, 2*np.pi, 24))
        })[x]
        
        model.predict.return_value = forecast
        yield mock_prophet

def test_prophet_anomaly_detection_normal(mock_data, mock_prophet):
    """测试正常路径"""
    logging.info("Starting normal test case")
    
    result = prophet_anomaly_detection(
        all_history=mock_data["all_history"],
        today=mock_data["today"],
        start_time=mock_data["start_time"],
        min_anomaly_run=3,
        sigma_soft=3,
        sigma_hard=5,
        sigma_diff=5
    )
    
    status, is_anomaly, anomaly_times, img_main, img_all_data, all_imgs = result
    
    # 验证基本返回类型
    assert isinstance(is_anomaly, bool)
    assert isinstance(anomaly_times, list)
    assert all(isinstance(t, datetime) for t in anomaly_times)
    assert isinstance(img_main, str)
    assert isinstance(all_imgs, dict)
    
    # 验证应该检测到异常
    assert is_anomaly is True
    assert len(anomaly_times) >= 3  # 我们添加了3个异常点
    
    # 验证图片base64编码
    assert img_main.startswith("iVBORw0KGgoAAAANSUhEUgAAB")

def test_prophet_anomaly_detection_no_anomaly(mock_data, mock_prophet):
    """测试无异常情况"""
    logging.info("Starting no-anomaly test case")
    
    # 修改today数据使其没有异常
    normal_today = [
        {"timestamp": mock_data["today"][i]["timestamp"], "value": 12 + np.sin(i/24)*2}
        for i in range(len(mock_data["today"]))
    ]
    
    result = prophet_anomaly_detection(
        all_history=mock_data["all_history"],
        today=normal_today,
        start_time=mock_data["start_time"],
        min_anomaly_run=3,
        sigma_soft=3,
        sigma_hard=5,
        sigma_diff=5
    )
    
    status, is_anomaly, anomaly_times, _, _, _ = result
    
    # 验证没有检测到异常
    assert is_anomaly is False
    assert len(anomaly_times) == 0

def test_prophet_anomaly_detection_insufficient_data(mock_data, mock_prophet):
    """测试数据不足的情况"""
    logging.info("Starting insufficient data test case")
    
    # 使用极少的历史数据
    minimal_history = mock_data["all_history"][:24]  # 仅1天数据
    
    result = prophet_anomaly_detection(
        all_history=minimal_history,
        today=mock_data["today"],
        start_time=mock_data["start_time"],
        min_anomaly_run=3,
        sigma_soft=3,
        sigma_hard=5,
        sigma_diff=5
    )
    
    status, is_anomaly, anomaly_times, _, _, _ = result
    
    # 验证函数仍然能运行并返回结果
    assert isinstance(is_anomaly, bool)
    assert isinstance(anomaly_times, list)
    
    # 由于数据不足，可能检测不到异常或误报
    # 这里只验证函数能正常运行而不崩溃

