#!/bin/bash
set -ex
cd `dirname $0`

# A special check for CLI users (run.sh should be located at the 'root' dir)
if [ -d "output" ]; then
    cd ./output/
fi

export RUNTIME_LOGDIR=/opt/tiger/toutiao/log
export PYTHONPATH=./site-packages


workers=$((MY_CPU_LIMIT))
timeout_sec=$((_BYTEFAAS_FUNC_TIMEOUT+_BYTEFAAS_COLD_START_TIMEOUT))
exec python3 -m bytedunicorn app:app --worker-class sync --workers $workers --bind [::]:$_BYTEFAAS_RUNTIME_PORT --timeout $timeout_sec --graceful-timeout $_BYTEFAAS_FUNC_TIMEOUT
