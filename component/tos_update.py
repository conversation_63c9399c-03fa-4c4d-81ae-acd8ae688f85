import time
import os
os.environ['TZ'] = 'UTC'
time.tzset()
import traceback
import bytedenv
import bytedtos
import json
import concurrent
import sys
from concurrent.futures import ProcessPoolExecutor, as_completed
from multiprocessing import cpu_count

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from component.data_center import query_metrics
from component.tcc_client import get_tcc, get_metrics_query_list, model_tag_names
from component.prophet_detection import prophet_decompose, smooth_series, save_prophet_model, preprocess_history_data
from io import BytesIO
import logging
from context.env import is_prod_env
from component.tos_client import tos_client
import pandas as pd
import numpy as np
from datetime import datetime

def get_last_two_2hour_timestamps():
    now = int(time.time())
    # 当前时间向下取整到最近的1小时
    this_1hour = now - (now % 3600)
    # 查询最近2小时的数据
    last_2hour = this_1hour - 2 * 3600
    return last_2hour, this_1hour

def train_and_upload_model(client, vregion, metric_query, tos_data, end_time):
    """
    训练Prophet模型并上传到TOS

    Args:
        client: TOS客户端
        vregion: 区域
        metric_query: 指标查询字符串
        tos_data: TOS中的历史数据
        end_time: 当前更新的结束时间戳
    """
    try:
        # 从tos_data中提取所有可用数据用于平滑处理
        all_values = tos_data[0]["values"]
        # 打印当前数据时间范围
        if all_values:
            first_time = datetime.fromtimestamp(all_values[0]["timestamp"])
            last_time = datetime.fromtimestamp(all_values[-1]["timestamp"])
            logging.info(f"当前数据时间范围: {first_time} 到 {last_time}")

        logging.info(f"开始训练Prophet模型:{vregion} {metric_query}, TOS总数据点数: {len(all_values)}")

        # 限制训练数据：只使用end_time前七天内的数据
        SEVEN_DAYS = 7 * 24 * 3600
        min_training_timestamp = end_time - SEVEN_DAYS

        # 过滤出七天内的数据
        recent_values = [item for item in all_values if item["timestamp"] >= min_training_timestamp]

        if not recent_values:
            logging.warning(f"七天内没有数据，跳过模型训练: {vregion} {metric_query}")
            return

        # 检查七天内的数据是否足够（至少需要48小时的数据点，半分钟一个点）
        if len(recent_values) < 48 * 60 * 2:  # 48小时 * 60分钟 * 2个点/分钟 = 5760个点
            logging.info(f"七天内训练数据不足，跳过模型训练: {vregion} {metric_query}, 数据点数: {len(recent_values)}")
            return

        logging.info(f"使用最近七天数据进行训练: {len(recent_values)} 个数据点 (总数据: {len(all_values)})")

        # 使用新的预处理函数进行噪声过滤和平滑处理
        df_training_series, outlier_indices, noise_filter_plot = preprocess_history_data(
            recent_values,
            noise_filter_method="iqr",  # 使用IQR方法进行噪声过滤
            noise_threshold=3.0,
            smoothing_window=3,
            smoothing_method="mean",
            create_noise_plot=True  # 创建噪声过滤图表
        )

        training_times = df_training_series.index.tolist()
        logging.info(f"预处理完成 - 原始数据: {len(recent_values)} 点, 处理后数据: {len(df_training_series)} 点, 异常点: {len(outlier_indices)} 个")
        logging.info(f"实际训练数据时间范围: {training_times[0]} 到 {training_times[-1]}")

        # 使用prophet_decompose进行训练
        train_start_time = time.time()
        model = prophet_decompose(df_training_series)
        train_end_time = time.time()
        logging.info(f"模型训练完成，训练用时: {train_end_time - train_start_time:.2f}秒")

        # 获取训练数据的最后时间戳
        training_data_end_timestamp = int(training_times[-1].timestamp())
        # 保存模型时传入训练数据的最后时间戳、训练数据Series、异常值索引和噪声过滤图表
        model_data = save_prophet_model(
            model,
            training_data_end_timestamp,
            df_training_series,
            outlier_indices,
            noise_filter_plot
        )
        training_data_end_time = datetime.fromtimestamp(training_data_end_timestamp)
        logging.info(f"最后一个训练数据的时间: {training_data_end_time}")
        logging.info(f"模型数据包含: 异常值索引({len(outlier_indices)}个), 噪声过滤图表({len(noise_filter_plot) if noise_filter_plot else 0}字符)")

        # 计算模型文件路径的时间戳（使用end_time保持统一），使用yyyy-MM-dd_HH格式
        model_timestamp_str = datetime.fromtimestamp(end_time).strftime("%Y_%m_%d_%H")
        model_key = f"anomaly_detection_metrics/models/{vregion}/{metric_query}/{model_timestamp_str}"

        # 上传模型到TOS
        resp = client.put_object(
            key=model_key,
            data=BytesIO(json.dumps(model_data).encode("utf-8")),
        )
        logging.info(f"Prophet模型上传成功: {model_key}")

    except Exception as e:
        logging.error(f"训练和上传模型失败: {metric_query}, {traceback.format_exc()}")

def process_single_config(args):
    """
    处理单个配置项的函数，用于多进程执行，支持重试机制

    Args:
        args: tuple包含(config_idx, vregion, query, start_time, end_time)

    Returns:
        dict: 处理结果
    """
    config_idx, vregion, query, start_time, end_time = args

    # 固定的重试参数
    max_retries = 3
    retry_delay = 1.0

    last_error = None

    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                logging.info(f"配置项 {config_idx + 1} 重试第 {attempt} 次: {vregion}/{query}")
                time.sleep(retry_delay)  # 递增延迟

            # 获取TOS客户端
            client = tos_client.client
            if not client:
                raise Exception("获取TOS客户端失败")

            logging.info(f"开始处理配置项 {config_idx + 1}, vregion: {vregion}, query: {query}")

            # 直接查询最近2小时的数据，包含所有dimensions
            logging.info(f"开始查询最近2小时数据: {vregion} {query}")
            result = query_metrics(
                start_time=start_time,
                end_time=end_time,
                vregion=vregion,
                query=query,
                block_cache=False,
                log=True,
            )

            if not result:
                logging.info(f"查询结果为空，跳过: {vregion} {query}")
                return {"config_idx": config_idx, "status": "skipped", "msg": "查询结果为空"}

            processed_dimensions = 0

            # 处理每个dimension的数据
            for dimension_idx, dimension_data in enumerate(result):
                dimensions = dimension_data.get("dimensions", {})
                values = dimension_data.get("values", [])

                if not values:
                    logging.info(f"维度 {dimension_idx + 1} 数据为空，跳过")
                    continue

                # 构造具体的metric_query
                dims_str = "{" + ",".join([f"{k}={v}" for k, v in sorted(dimensions.items())]) + "}"

                # 找到第一个{和第一个}来构造完整query
                first_l = query.find("{")
                first_r = query.find("}")
                prefix_metric = query[:first_l]  # 第一个{之前作为前缀
                suffix_metric = query[first_r + 1:]  # 第一个}之后作为后缀
                metric_query = f"{prefix_metric}{dims_str}{suffix_metric}"

                logging.info(f"处理维度 {dimension_idx + 1}: {metric_query}")

                # 更新TOS数据
                try:
                    resp = client.get_object(key=f"anomaly_detection_metrics/{vregion}/{metric_query}")
                    tos_data = json.loads(resp.data.decode("utf-8"))
                    logging.info(f"TOS数据获取成功: {metric_query}")

                    # 存在数据，获取最后时间戳并只拼接新数据
                    # 兼容旧数据格式：如果没有end_time字段，从values中获取最后时间戳
                    if "end_time" in tos_data[0] and tos_data[0]["end_time"]:
                        last_timestamp = tos_data[0]["end_time"]
                    elif tos_data[0]["values"]:
                        last_timestamp = tos_data[0]["values"][-1]["timestamp"]
                        logging.info(f"旧数据格式，从values获取最后时间戳: {last_timestamp}")
                    else:
                        last_timestamp = 0
                        logging.warning(f"数据异常，values为空: {metric_query}")
                    last_time = datetime.fromtimestamp(last_timestamp)
                    logging.info(f"TOS数据最新时间: {last_time}")

                    # 只保留时间戳大于last_timestamp的数据
                    new_values = [v for v in values if v["timestamp"] > last_timestamp]

                    if new_values:
                        logging.info(f"查询到 {len(new_values)} 个新数据点，开始更新")
                        # 拼接新数据
                        tos_data[0]["values"].extend(new_values)

                        # 数据去重和排序（在拼接后进行）
                        all_values = tos_data[0]["values"]

                        # 使用字典去重，保留最新的数据点
                        unique_values = {}
                        for item in all_values:
                            timestamp = item["timestamp"]
                            unique_values[timestamp] = item

                        # 转换回列表并按时间戳排序
                        deduplicated_values = list(unique_values.values())
                        deduplicated_values.sort(key=lambda x: x["timestamp"])

                        removed_count = len(all_values) - len(deduplicated_values)
                        if removed_count > 0:
                            logging.info(f"去重完成: 移除 {removed_count} 个重复数据点")

                        # 更新去重后的数据
                        tos_data[0]["values"] = deduplicated_values

                        # 更新end_time
                        tos_data[0]["end_time"] = deduplicated_values[-1]["timestamp"]

                        # 保留end_time前30天的数据，并更新start_time
                        THIRTY_DAYS = 30 * 24 * 3600
                        min_timestamp = tos_data[0]["end_time"] - THIRTY_DAYS
                        # 只保留timestamp >= min_timestamp的数据
                        filtered_values = [
                            v for v in deduplicated_values if v["timestamp"] >= min_timestamp
                        ]
                        tos_data[0]["values"] = filtered_values

                        # 确保start_time和end_time字段存在并正确设置
                        if filtered_values:
                            tos_data[0]["start_time"] = filtered_values[0]["timestamp"]
                            tos_data[0]["end_time"] = filtered_values[-1]["timestamp"]
                        else:
                            # 如果过滤后没有数据，保持原有的时间戳
                            if "start_time" not in tos_data[0]:
                                tos_data[0]["start_time"] = deduplicated_values[0]["timestamp"]
                            if "end_time" not in tos_data[0]:
                                tos_data[0]["end_time"] = deduplicated_values[-1]["timestamp"]
                        tos_start_time = datetime.fromtimestamp(tos_data[0]["start_time"])
                        tos_end_time = datetime.fromtimestamp(tos_data[0]["end_time"])
                        logging.info(f"更新后数据时间范围: {tos_start_time} - {tos_end_time}")

                        # 重新上传
                        resp = client.put_object(
                            key=f"anomaly_detection_metrics/{vregion}/{metric_query}",
                            data=BytesIO(json.dumps(tos_data).encode("utf-8")),
                        )
                        logging.info(f"TOS数据更新成功: {metric_query}, 新增 {len(new_values)} 个数据点")

                        # 训练和上传模型
                        train_and_upload_model(client, vregion, metric_query, tos_data, end_time)
                    else:
                        logging.info(f"没有新数据需要更新: {metric_query}")

                except bytedtos.TosException as e:
                    if e.code == 404:
                        # TOS中不存在该指标，创建新的
                        logging.info(f"TOS中不存在指标数据，开始创建: {metric_query}")

                        # 对新数据进行去重和排序
                        unique_values = {}
                        for item in values:
                            timestamp = item["timestamp"]
                            unique_values[timestamp] = item

                        deduplicated_values = list(unique_values.values())
                        deduplicated_values.sort(key=lambda x: x["timestamp"])

                        removed_count = len(values) - len(deduplicated_values)
                        if removed_count > 0:
                            logging.info(f"新数据去重: 移除 {removed_count} 个重复数据点")

                        tos_data = [{
                            "dimensions": dimensions,
                            "values": deduplicated_values,
                            "start_time": deduplicated_values[0]["timestamp"],
                            "end_time": deduplicated_values[-1]["timestamp"]
                        }]
                        start_time = datetime.fromtimestamp(tos_data[0]["start_time"])
                        end_time = datetime.fromtimestamp(tos_data[0]["end_time"])
                        logging.info(f"新数据时间范围: {start_time} - {end_time}")

                        resp = client.put_object(
                            key=f"anomaly_detection_metrics/{vregion}/{metric_query}",
                            data=BytesIO(json.dumps(tos_data).encode("utf-8")),
                        )
                        logging.info(f"TOS数据创建成功: {metric_query}, 共 {len(deduplicated_values)} 个数据点")

                        # 训练和上传模型
                        train_and_upload_model(client, vregion, metric_query, tos_data, end_time)
                    else:
                        logging.error(f"TOS操作异常: {metric_query}, {str(e)}")
                        continue

                processed_dimensions += 1

            # 成功处理，返回结果
            return {
                "config_idx": config_idx,
                "status": "success",
                "processed_dimensions": processed_dimensions,
                "vregion": vregion,
                "query": query,
                "attempts": attempt + 1
            }

        except Exception as e:
            last_error = e
            error_msg = f"处理配置项失败 (尝试 {attempt + 1}/{max_retries + 1}): {config_idx + 1}, {vregion}, {query}, 错误: {str(e)}"

            if attempt < max_retries:
                logging.warning(error_msg + " - 将重试")
            else:
                logging.error(error_msg + " - 已达最大重试次数")

    # 所有重试都失败了
    return {
        "config_idx": config_idx,
        "status": "error",
        "msg": f"重试 {max_retries + 1} 次后仍失败: {str(last_error)}",
        "vregion": vregion,
        "query": query,
        "attempts": max_retries + 1
    }

# 根据TCC配置直接查询最近2小时的数据并更新TOS
def update_tos():
    """
    更新TOS数据，支持多进程和分布式执行

    环境变量:
        MAX_WORKERS: 最大工作进程数，默认为CPU核心数
        SHARD_INDEX: 当前分片索引（从0开始），默认为0
        TOTAL_SHARDS: 总分片数，默认为1
    """
    # 从环境变量读取配置
    max_workers = os.getenv('MAX_WORKERS')
    if max_workers is not None:
        max_workers = int(max_workers)

    shard_index = int(os.getenv('SHARD_INDEX', '0'))
    total_shards = int(os.getenv('TOTAL_SHARDS', '1'))
    # 从TCC获取查询语句列表
    query_list = get_metrics_query_list()
    if not query_list:
        logging.warning("未能从TCC获取查询语句列表")
        return {"code": 0, "msg": "No metrics to update"}
    
    # 固定的vregion数组
    vregion_list = [
        "US-East",
        "Singapore-Central", 
        "US-EastRed",
        "EU-TTP2",
        "US-TTP",
        "US-TTP2"
    ]
    
    # 构造所有配置项：query_list与vregion_list做笛卡尔积
    all_configs = []
    for query in query_list:
        for vregion in vregion_list:
            all_configs.append((vregion, query))

    # 分片处理：根据shard_index和total_shards分配任务
    if total_shards > 1:
        shard_size = len(all_configs) // total_shards
        start_idx = shard_index * shard_size
        if shard_index == total_shards - 1:
            # 最后一个分片处理剩余的所有配置
            end_idx = len(all_configs)
        else:
            end_idx = start_idx + shard_size

        configs_to_process = all_configs[start_idx:end_idx]
        logging.info(f"分片执行: 分片 {shard_index + 1}/{total_shards}, 处理配置 {start_idx + 1}-{end_idx}/{len(all_configs)}")
    else:
        start_idx = 0  # 单机执行时起始索引为0
        configs_to_process = all_configs
        logging.info(f"单机执行: 处理全部 {len(all_configs)} 个配置")

    if not configs_to_process:
        logging.info("当前分片没有配置需要处理")
        return {"code": 0, "msg": "No configs to process in this shard"}

    # 配置查询参数 - 查询最近2小时的数据
    start_time, end_time = get_last_two_2hour_timestamps()

    # 准备多进程参数
    process_args = []
    for local_idx, (vregion, query) in enumerate(configs_to_process):
        # 计算全局配置项ID：起始索引 + 本地索引
        global_config_idx = start_idx + local_idx
        process_args.append((global_config_idx, vregion, query, start_time, end_time))

    # 设置工作进程数
    if max_workers is None:
        max_workers = min(cpu_count(), len(process_args))

    logging.info(f"开始多进程更新TOS数据，工作进程数: {max_workers}, 配置项数: {len(process_args)}")
    update_tos_start_time = time.time()

    # 使用多进程执行
    success_count = 0
    error_count = 0
    skipped_count = 0

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_config = {executor.submit(process_single_config, args): args for args in process_args}

        # 收集结果
        for future in as_completed(future_to_config):
            try:
                result = future.result()
                attempts_info = f" (尝试 {result.get('attempts', 1)} 次)" if result.get('attempts', 1) > 1 else ""

                if result["status"] == "success":
                    success_count += 1
                    logging.info(f"配置项 {result['config_idx'] + 1} 处理成功: {result['vregion']}/{result['query']}, 处理维度数: {result.get('processed_dimensions', 0)}{attempts_info}")
                elif result["status"] == "skipped":
                    skipped_count += 1
                    logging.info(f"配置项 {result['config_idx'] + 1} 跳过: {result.get('msg', '')}{attempts_info}")
                else:
                    error_count += 1
                    logging.error(f"配置项 {result['config_idx'] + 1} 处理失败: {result.get('msg', '')}{attempts_info}")
            except Exception as e:
                error_count += 1
                logging.error(f"获取处理结果失败: {str(e)}")

    update_tos_end_time = time.time()
    total_time = update_tos_end_time - update_tos_start_time

    logging.info(f"TOS数据更新完成 - 总耗时: {total_time:.2f}秒, 成功: {success_count}, 跳过: {skipped_count}, 失败: {error_count}")

    return {
        "code": 0,
        "msg": "TOS update completed",
        "stats": {
            "total_time": total_time,
            "success_count": success_count,
            "skipped_count": skipped_count,
            "error_count": error_count,
            "total_configs": len(process_args)
        }
    }

def main():
    """主函数，从环境变量读取配置并执行TOS更新"""
    # 从环境变量读取配置
    max_workers = os.getenv('MAX_WORKERS')
    shard_index = int(os.getenv('SHARD_INDEX', '0'))
    total_shards = int(os.getenv('TOTAL_SHARDS', '1'))

    # 参数验证
    if shard_index < 0:
        print("错误: SHARD_INDEX 必须 >= 0")
        return 1

    if total_shards < 1:
        print("错误: TOTAL_SHARDS 必须 >= 1")
        return 1

    if shard_index >= total_shards:
        print("错误: SHARD_INDEX 必须小于 TOTAL_SHARDS")
        return 1

    if max_workers is not None:
        max_workers = int(max_workers)
        if max_workers < 1:
            print("错误: MAX_WORKERS 必须 >= 1")
            return 1

    # 配置日志系统
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    logging.info(f"当前TCE_HOST_ENV: {os.environ.get('TCE_HOST_ENV')}")
    logging.info("=== 开始执行 TOS 更新任务 ===")
    logging.info(f"执行参数: max_workers={max_workers}, shard_index={shard_index}, total_shards={total_shards}")

    # 执行更新
    try:
        result = update_tos()

        logging.info(f"=== TOS 更新任务完成，结果: {result} ===")

        if result["code"] == 0:
            return 0
        else:
            return 1

    except Exception as e:
        logging.error(f"=== TOS 更新任务执行失败: {str(e)} ===")
        logging.error(f"错误详情: {traceback.format_exc()}")
        print({"code": 5, "msg": str(e), "stacktrace": traceback.format_exc()})
        return 1

if __name__ == "__main__":
    exit(main())