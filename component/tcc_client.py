import os
os.environ['CONSUL_HTTP_HOST'] = '***********'
import sys
# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import requests
from typing import Dict, Any, Optional
import logging
import json
from context.env import is_prod_env

model_tag_names = ["model_name", "model_version", "model", "cta_version", "vta_version"]
def get_tcc(key) -> Optional[Dict[str, Any]]:
    """
    获取TCC配置
    :param key: 配置名称
    :return: 配置数据，如果获取失败则返回None
    """
    
    if is_prod_env:
        url = "https://paas-gw-i18n.byted.org/bcc/open/config/get"
        secret_key = "af3fae6cf79df3a5df0e4220caa6f70f"
        params = {
            "ns_name": "ad.model.meta",
            "dir": "/default",
            "region": "Singapore-Central",
            "conf_name": key
        }
    else:
        url = "https://paas-gw-boe.byted.org/bcc/open/config/get"
        secret_key = "94f3df4963d33c85f419e68fa525f521"
        params = {
            "ns_name": "ad.qa.ad_monitor",
            "dir": "/default",
            "region": "China-BOE",
            "conf_name": key
        }
    headers = {
        "domain": "tcc_v3_openapi;v1",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {secret_key}"
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        
        result = response.json()
        
        # 提取data.version_data.data并转换为JSON对象
        config_data = result.get("data", {}).get("version_data", {}).get("data")
        if config_data:
            parsed_config = json.loads(config_data)
            logging.info(f"成功获取TCC配置: {key}")
            return parsed_config
        else:
            logging.warning(f"未找到配置数据: {key}")
            return None
            
    except requests.exceptions.RequestException as e:
        logging.error(f"获取TCC配置失败: {e}")
        return None
    except (ValueError, json.JSONDecodeError) as e:
        logging.error(f"JSON解析失败: {e}")
        return None
    
def update_tcc(key, value) -> Optional[Dict[str, Any]]:
    """
    更新TCC配置
    :param key: 配置名称
    :return: 配置数据，如果更新失败则返回None
    """
    
    if is_prod_env:
        url = "https://paas-gw-i18n.byted.org/bcc/open/config/update"
        secret_key = "af3fae6cf79df3a5df0e4220caa6f70f"
        data = {
            "ns_name": "ad.model.meta",
            "dir": "/default",
            "operator": "sunhongwei.02",
            "conf_name": key,
            "value": value,
            "data_type": "json",
            "regions": ["Singapore-Central","US-East"]
        }
        
    else:
        url = "https://paas-gw-boe.byted.org/bcc/open/config/update"
        secret_key = "94f3df4963d33c85f419e68fa525f521"
        data = {
            "ns_name": "ad.qa.ad_monitor",
            "dir": "/default",
            "operator": "sunhongwei.02",
            "conf_name": key,
            "value": value,
            "data_type": "json",
            "region": "China-BOE"
        }
    headers = {
        "domain": "tcc_v3_openapi;v1",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {secret_key}"
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get("base_resp", {}).get("error_code") == 0:
            logging.info(f"成功更新TCC配置: {key}")
            return value
    except requests.exceptions.RequestException as e:
        logging.error(f"获取TCC配置失败: {e}")
        return None
    except (ValueError, json.JSONDecodeError) as e:
        logging.error(f"JSON解析失败: {e}")
        return None

def get_metrics_cfg_list():
    """
    从front_end字段下一级的scenes字段的下一级提取所有queries对象
    :return: 包含所有queries的JSON字符串，如果获取失败则返回空JSON数组
    """
    scenario_cfg_obj = get_tcc("cloister_conf").get("model_diagnosis", {}).get("abnormal_scene_config", {})
    if not scenario_cfg_obj:
        logging.warning("未能获取scenario_cfg_obj配置")
        return "[]"
    
    queries_list = []
    
    # 按固定路径访问front_end -> scenes -> queries
    front_end = scenario_cfg_obj.get("front_end")
    if front_end and isinstance(front_end, dict):
        # 遍历front_end下的所有一级对象（新增层级）
        for section in front_end.values():
            if isinstance(section, dict):
                scenes = section.get("scenes")
                if scenes and isinstance(scenes, dict):
                    for scene in scenes.values():
                        if isinstance(scene, dict):
                            # 获取场景对象下的一级对象（新增层级）
                            queries = scene.get("queries", {})
                            if queries and isinstance(queries, list):
                                queries_list.extend(queries)
    
    logging.info(f"成功提取{len(queries_list)}个queries对象")
    return queries_list

def get_metrics_query_list():
    """
    获取所有指标查询模板列表
    :return: 包含所有指标查询模板的列表
    """
    metrics_query_list = []
    metrics_template_str = "{aggregation_placeholder}:store:top-10-avg:{metrics_placeholder}{{{tag_placeholder}=literal_or({literal_value}),dc=literal_or({dc_placeholder}){free_str}}}"
    metrics_no_tag_template_str = "{aggregation_placeholder}:store:top-10-avg:{metrics_placeholder}{{dc=literal_or({dc_placeholder}){free_str}}}"
    metrics_cfg_list = get_metrics_cfg_list()
    for metrics_item in metrics_cfg_list:
        free_str = ""
        if metrics_item.get("other_tag") is not None:
            for tag_k, tag_v in metrics_item.get("other_tag").items():
                free_str += f",{tag_k}=literal_or({tag_v})"
        metrics = metrics_item.get("metrics")
        metrics_name = metrics_item.get("name")
        metrics_model_tag = metrics_item.get("tag", "model_name")
        metrics_down_sample = metrics_item.get("down_sample", 0.5)
        metrics_query = metrics_template_str.format(aggregation_placeholder="avg", metrics_placeholder=metrics, tag_placeholder=metrics_model_tag, literal_value="*", dc_placeholder="*", free_str=free_str)
        if metrics_model_tag in model_tag_names:
            metrics_query_no_model_tag = metrics_no_tag_template_str.format(aggregation_placeholder="avg", metrics_placeholder=metrics, dc_placeholder="*", free_str=free_str)
            metrics_query_list.append(metrics_query_no_model_tag)
        metrics_query_list.append(metrics_query)
    return metrics_query_list