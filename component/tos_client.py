from context.env import is_prod_env
import bytedtos

class TosClient:
    def __init__(self):
        self.client = self.get_tos_client()

    def get_tos_client(self):
        """
        获取 TOS 客户端
        """
        bucket_name = "ad-monitor-models"
        tos_psm = "toutiao.tos.tosapi"
        tos_cluster = "default"
        ak = "PV0NHNQXEXFB4IRE0RUP"

        if is_prod_env:
            tos_idc = "maliva"

            client = bytedtos.Client(
                bucket_name, ak, service=tos_psm, cluster=tos_cluster, idc=tos_idc
            )
            return client
        else:
            # 子域名、Accesskey 和 Bucket 可在TOS 用户平台 > Bucket 详情 > 概览页中查找。
            sub_domain = "tos-us.tiktok-row.org"
            is_stream = False

            client = bytedtos.Client(bucket_name, ak,
                    # endpoint 是可选参数，设置是否通过子域名初始化
                    endpoint=sub_domain,
                    force_endpoint=True,
                    # stream 是可选参数，设置是否流式下载
                    stream=is_stream,
                    # timeout 是可选参数，设置请求超时
                    timeout=60,
                    # connection_time 是可选参数，设置连接超时
                    connect_timeout=60,
                    # 设置connection_pool_size =10，将连接池大小设置为 10
                    connection_pool_size=10)   
            return client

tos_client = TosClient()