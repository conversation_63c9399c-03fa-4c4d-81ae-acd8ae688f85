import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional, Tuple
from scipy import interpolate
from scipy.stats import zscore
import plotly.graph_objects as go

# 定义UTC+8时区
UTC_PLUS_8 = timezone(timedelta(hours=8))

def convert_to_utc_plus_8(dt_list):
    """将UTC时间列表转换为UTC+8时间列表，仅用于画图显示"""
    return [dt.replace(tzinfo=timezone.utc).astimezone(UTC_PLUS_8) for dt in dt_list]


class DataPreprocessor:
    """
    数据预处理大模块，包含插值、噪声过滤和数据平滑功能
    
    输入输出格式适配现有训练模块的数据格式：
    - 输入：[{"timestamp": int, "value": float}, ...]
    - 输出：[{"timestamp": int, "value": float}, ...]
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def preprocess_data(
        self,
        data: List[Dict[str, Any]],
        interpolation_method: str = "linear",
        noise_filter_method: str = "zscore",
        noise_threshold: float = 3.0,
        smoothing_method: str = "SMA",
        smoothing_window: int = 3,
        normalization_method: str = "zscore"
    ) -> List[Dict[str, Any]]:
        """
        完整的数据预处理流程

        预处理步骤：
        1. 数据插值（中间部分缺失值）
        2. 噪声过滤（剔除历史异常值）
        3. 数据平滑
        4. 数据标准化

        Args:
            data: 原始数据列表，格式为 [{"timestamp": int, "value": float}, ...]
            interpolation_method: 插值方法，支持 "linear", "cubic", "nearest"
            noise_filter_method: 噪声过滤方法，支持 "zscore", "iqr", "none"
            noise_threshold: 噪声过滤阈值
            smoothing_method: 平滑方法，支持 "SMA", "EMA", "WMA"
            smoothing_window: 平滑窗口大小
            normalization_method: 标准化方法，支持 "zscore", "minmax", "robust"

        Returns:
            预处理后的数据列表，格式与输入相同，数据已标准化
        """
        if not data:
            self.logger.warning("输入数据为空")
            return []
        
        self.logger.info(f"开始数据预处理，原始数据点数: {len(data)}")
        
        # 步骤1: 数据插值（中间部分缺失值）
        interpolated_data = self.interpolate_missing_values(data, method=interpolation_method)
        self.logger.info(f"插值处理完成，数据点数: {len(interpolated_data)}")
        
        # 步骤2: 噪声过滤（剔除历史异常值）
        filtered_data, _ = self.filter_noise(interpolated_data, method=noise_filter_method, threshold=noise_threshold)
        self.logger.info(f"噪声过滤完成，数据点数: {len(filtered_data)}")
        
        # 步骤3: 数据平滑
        smoothed_data = self.smooth_data(filtered_data, method=smoothing_method, window=smoothing_window)
        self.logger.info(f"数据平滑完成，数据点数: {len(smoothed_data)}")

        # 步骤4: 数据标准化
        normalized_data = self.normalize_data(smoothed_data, method=normalization_method)
        self.logger.info(f"数据标准化完成，方法: {normalization_method}")

        return normalized_data
    
    def interpolate_missing_values(
        self, 
        data: List[Dict[str, Any]], 
        method: str = "linear"
    ) -> List[Dict[str, Any]]:
        """
        插值处理中间部分的缺失数据，开头结尾不做处理
        
        Args:
            data: 输入数据
            method: 插值方法 ("linear", "cubic", "nearest")
            
        Returns:
            插值后的数据
        """
        if len(data) < 3:
            self.logger.warning("数据点数少于3个，跳过插值处理")
            return data.copy()
        
        # 转换为DataFrame便于处理
        df = pd.DataFrame(data)
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # 检查时间序列的规律性，推断期望的时间间隔
        time_diffs = np.diff(df['timestamp'])
        expected_interval = np.median(time_diffs)
        
        # 找出中间部分的缺失时间点（不包括开头和结尾）
        missing_timestamps = []
        start_time = df['timestamp'].iloc[0]
        end_time = df['timestamp'].iloc[-1]
        
        current_time = start_time + expected_interval
        existing_timestamps = set(df['timestamp'])
        
        while current_time < end_time:
            if current_time not in existing_timestamps:
                # 检查这个缺失点是否在中间部分（前后都有数据点）
                has_before = any(t < current_time for t in existing_timestamps)
                has_after = any(t > current_time for t in existing_timestamps)
                if has_before and has_after:
                    missing_timestamps.append(current_time)
            current_time += expected_interval
        
        if not missing_timestamps:
            self.logger.info("没有发现中间部分的缺失数据点")
            return data.copy()
        
        self.logger.info(f"发现 {len(missing_timestamps)} 个中间缺失数据点，开始插值")
        
        # 执行插值
        timestamps = df['timestamp'].values
        values = df['value'].values
        
        # 创建插值函数
        if method == "linear":
            f = interpolate.interp1d(timestamps, values, kind='linear', bounds_error=False, fill_value='extrapolate')
        elif method == "cubic":
            if len(timestamps) >= 4:  # 三次插值至少需要4个点
                f = interpolate.interp1d(timestamps, values, kind='cubic', bounds_error=False, fill_value='extrapolate')
            else:
                self.logger.warning("数据点不足4个，降级为线性插值")
                f = interpolate.interp1d(timestamps, values, kind='linear', bounds_error=False, fill_value='extrapolate')
        elif method == "nearest":
            f = interpolate.interp1d(timestamps, values, kind='nearest', bounds_error=False, fill_value='extrapolate')
        else:
            raise ValueError(f"不支持的插值方法: {method}")
        
        # 插值缺失点
        interpolated_values = f(missing_timestamps)
        
        # 合并原始数据和插值数据
        result_data = data.copy()
        for i, timestamp in enumerate(missing_timestamps):
            result_data.append({
                "timestamp": int(timestamp),
                "value": float(interpolated_values[i])
            })
        
        # 按时间戳排序
        result_data.sort(key=lambda x: x['timestamp'])

        return result_data

    def filter_noise(
        self,
        data: List[Dict[str, Any]],
        method: str = "zscore",
        threshold: float = 3.0,
        create_plot: bool = False
    ) -> Tuple[List[Dict[str, Any]], List[int], Optional[str]]:
        """
        噪声过滤，剔除历史异常值

        Args:
            data: 输入数据
            method: 过滤方法 ("zscore", "iqr", "none")
            threshold: 过滤阈值
            create_plot: 是否创建噪声过滤可视化图表

        Returns:
            Tuple[过滤后的数据, 噪声点的时间戳索引列表, 可视化图表HTML(如果create_plot=True)]
        """
        if not data or method == "none":
            return data.copy(), [], None

        if len(data) < 10:
            self.logger.warning("数据点数少于10个，跳过噪声过滤")
            return data.copy(), [], None

        df = pd.DataFrame(data)
        df = df.sort_values('timestamp').reset_index(drop=True)
        values = df['value'].values

        if method == "zscore":
            # Z-score方法：移除超过threshold个标准差的点
            z_scores = np.abs(zscore(values))
            outlier_mask = z_scores > threshold

        elif method == "iqr":
            # IQR方法：移除超出1.5*IQR范围的点
            Q1 = np.percentile(values, 25)
            Q3 = np.percentile(values, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            outlier_mask = (values < lower_bound) | (values > upper_bound)

        else:
            raise ValueError(f"不支持的噪声过滤方法: {method}")

        # 记录被过滤的异常点数量和时间戳索引
        outlier_indices = np.where(outlier_mask)[0]
        outlier_timestamps = df.iloc[outlier_indices]['timestamp'].tolist()
        outlier_count = len(outlier_indices)

        if outlier_count > 0:
            self.logger.info(f"使用{method}方法过滤了 {outlier_count} 个异常点")

        # 保留非异常点
        filtered_df = df[~outlier_mask]

        # 转换回原始格式
        result_data = []
        for _, row in filtered_df.iterrows():
            result_data.append({
                "timestamp": int(row['timestamp']),
                "value": float(row['value'])
            })

        # 创建可视化图表（如果需要）
        plot_html = None
        if create_plot:
            plot_html = self._create_noise_filter_plot(
                df, values, outlier_mask, method, threshold,
                upper_bound if method == "iqr" else None,
                lower_bound if method == "iqr" else None
            )

        return result_data, outlier_timestamps, plot_html

    def smooth_data(
        self,
        data: List[Dict[str, Any]],
        method: str = "SMA",
        window: int = 3
    ) -> List[Dict[str, Any]]:
        """
        数据平滑处理

        Args:
            data: 输入数据
            method: 平滑方法 ("SMA", "EMA", "WMA")
            window: 平滑窗口大小

        Returns:
            平滑后的数据
        """
        if not data or window <= 1:
            return data.copy()

        if len(data) < window:
            self.logger.warning(f"数据点数({len(data)})少于窗口大小({window})，跳过平滑处理")
            return data.copy()

        df = pd.DataFrame(data)
        df = df.sort_values('timestamp').reset_index(drop=True)
        values = df['value'].values

        if method == "SMA":
            # 简单移动平均 (Simple Moving Average)
            smoothed_values = self._simple_moving_average(values, window)

        elif method == "EMA":
            # 指数移动平均 (Exponential Moving Average)
            smoothed_values = self._exponential_moving_average(values, window)

        elif method == "WMA":
            # 加权移动平均 (Weighted Moving Average)
            smoothed_values = self._weighted_moving_average(values, window)

        else:
            raise ValueError(f"不支持的平滑方法: {method}")

        # 转换回原始格式
        result_data = []
        for i, row in df.iterrows():
            result_data.append({
                "timestamp": int(row['timestamp']),
                "value": float(smoothed_values[i])
            })

        return result_data

    def _simple_moving_average(self, values: np.ndarray, window: int) -> np.ndarray:
        """简单移动平均"""
        smoothed = np.zeros_like(values)
        for i in range(len(values)):
            start_idx = max(0, i - window // 2)
            end_idx = min(len(values), i + window // 2 + 1)
            smoothed[i] = np.mean(values[start_idx:end_idx])
        return smoothed

    def _exponential_moving_average(self, values: np.ndarray, window: int) -> np.ndarray:
        """指数移动平均"""
        alpha = 2.0 / (window + 1)
        smoothed = np.zeros_like(values)
        smoothed[0] = values[0]

        for i in range(1, len(values)):
            smoothed[i] = alpha * values[i] + (1 - alpha) * smoothed[i-1]

        return smoothed

    def _weighted_moving_average(self, values: np.ndarray, window: int) -> np.ndarray:
        """加权移动平均"""
        # 创建权重：中心权重最大，向两边递减
        weights = np.arange(1, window + 1)
        weights = np.concatenate([weights, weights[-2::-1]]) if window > 1 else weights
        weights = weights / np.sum(weights)

        smoothed = np.zeros_like(values)
        half_window = window // 2

        for i in range(len(values)):
            start_idx = max(0, i - half_window)
            end_idx = min(len(values), i + half_window + 1)

            # 调整权重以匹配实际窗口大小
            actual_window = end_idx - start_idx
            if actual_window != len(weights):
                # 重新计算权重
                actual_weights = np.arange(1, actual_window + 1)
                if actual_window > 1:
                    actual_weights = np.minimum(actual_weights, actual_weights[::-1])
                actual_weights = actual_weights / np.sum(actual_weights)
            else:
                actual_weights = weights

            smoothed[i] = np.average(values[start_idx:end_idx], weights=actual_weights)

        return smoothed

    def _create_noise_filter_plot(
        self,
        df: pd.DataFrame,
        values: np.ndarray,
        outlier_mask: np.ndarray,
        method: str,
        threshold: float,
        upper_bound: Optional[float] = None,
        lower_bound: Optional[float] = None
    ) -> str:
        """
        创建噪声过滤可视化图表

        Args:
            df: 原始数据DataFrame
            values: 数值数组
            outlier_mask: 异常值掩码
            method: 过滤方法
            threshold: 阈值
            upper_bound: 上界（IQR方法使用）
            lower_bound: 下界（IQR方法使用）

        Returns:
            图表的HTML字符串
        """
        fig = go.Figure()

        # 转换时间为UTC+8用于显示
        timestamps = [datetime.fromtimestamp(ts) for ts in df['timestamp']]
        times_utc8 = convert_to_utc_plus_8(timestamps)

        # 计算阈值范围
        if method == "zscore":
            mean_val = np.mean(values)
            std_val = np.std(values)
            upper_threshold = mean_val + threshold * std_val
            lower_threshold = mean_val - threshold * std_val
        elif method == "iqr":
            upper_threshold = upper_bound
            lower_threshold = lower_bound
        else:
            upper_threshold = np.max(values)
            lower_threshold = np.min(values)

        # 添加阈值范围（灰色填充区域）
        fig.add_trace(go.Scatter(
            x=times_utc8,
            y=[upper_threshold] * len(times_utc8),
            mode='lines',
            line=dict(color="rgba(0,0,0,0)", width=0),
            showlegend=False
        ))
        fig.add_trace(go.Scatter(
            x=times_utc8,
            y=[lower_threshold] * len(times_utc8),
            mode='lines',
            line=dict(color="rgba(0,0,0,0)", width=0),
            fill='tonexty',
            fillcolor='rgba(220,220,220,0.7)',
            name=f"{method.upper()} {threshold}σ Range",
            showlegend=False
        ))

        # 添加正常数据线（蓝色）
        normal_mask = ~outlier_mask
        if np.any(normal_mask):
            normal_times = [times_utc8[i] for i in range(len(times_utc8)) if normal_mask[i]]
            normal_values = values[normal_mask]

            fig.add_trace(go.Scatter(
                x=normal_times,
                y=normal_values,
                mode='lines',
                name="Normal Data",
                line=dict(color="#4A90E2", width=1),
                showlegend=False,
                hovertemplate='<b>正常数据</b><br>' +
                             '时间: %{x|%m-%d %H:%M}<br>' +
                             '值: %{y:.5f}<br>' +
                             '<extra></extra>'
            ))

        # 添加异常数据线段（紫色）
        if np.any(outlier_mask):
            outlier_indices = np.where(outlier_mask)[0]
            for idx in outlier_indices:
                # 创建包含前后点的小线段
                segment_times = []
                segment_values = []

                # 添加前一个点（如果存在且不是异常值）
                if idx > 0 and not outlier_mask[idx - 1]:
                    segment_times.append(times_utc8[idx - 1])
                    segment_values.append(values[idx - 1])

                # 添加异常点
                segment_times.append(times_utc8[idx])
                segment_values.append(values[idx])

                # 添加后一个点（如果存在且不是异常值）
                if idx < len(values) - 1 and not outlier_mask[idx + 1]:
                    segment_times.append(times_utc8[idx + 1])
                    segment_values.append(values[idx + 1])

                # 添加紫色线段
                fig.add_trace(go.Scatter(
                    x=segment_times,
                    y=segment_values,
                    mode='lines',
                    name="Outliers",
                    line=dict(color="#8A2BE2", width=2),  # 紫色
                    showlegend=False,
                    hovertemplate='<b>异常数据</b><br>' +
                                 '时间: %{x|%m-%d %H:%M}<br>' +
                                 '值: %{y:.5f}<br>' +
                                 '<extra></extra>'
                ))

        fig.update_layout(
            title=f"Noise Filtering - {method.upper()} Method (Threshold: {threshold})",
            margin=dict(l=0, r=0, t=50, b=50),
            showlegend=False,
            plot_bgcolor='white',
            paper_bgcolor='white',
            xaxis=dict(
                title="Time",
                tickformat="%m-%d %H:%M",
                showline=False,
                showgrid=False,
                zeroline=False,
                rangeslider=dict(
                    visible=True,
                    thickness=0.1,
                    bgcolor="rgba(240,240,240,0.8)",
                    bordercolor="rgba(200,200,200,0.8)",
                    borderwidth=1
                ),
                type="date"
            ),
            yaxis=dict(
                title="Value",
                showline=False,
                showgrid=False,
                zeroline=False
            )
        )

        return fig.to_html(
            include_plotlyjs='inline',
            config={'displayModeBar': False}
        )

    # 独立的数据预处理方法，用于为其他模块提供接口
    def handle_missing_values(
        self,
        data: List[Dict[str, Any]],
        method: str = "linear"
    ) -> List[Dict[str, Any]]:
        """
        独立的数据缺省处理方法

        Args:
            data: 输入数据
            method: 插值方法 ("linear", "cubic", "nearest")

        Returns:
            插值后的数据
        """
        return self.interpolate_missing_values(data, method)

    def remove_noise(
        self,
        data: List[Dict[str, Any]],
        method: str = "zscore",
        threshold: float = 3.0,
        create_plot: bool = False
    ) -> Tuple[List[Dict[str, Any]], List[int], Optional[str]]:
        """
        独立的噪声过滤方法

        Args:
            data: 输入数据
            method: 过滤方法 ("zscore", "iqr", "none")
            threshold: 过滤阈值
            create_plot: 是否创建可视化图表

        Returns:
            Tuple[过滤后的数据, 噪声点的时间戳索引列表, 可视化图表HTML(如果create_plot=True)]
        """
        return self.filter_noise(data, method, threshold, create_plot)

    def apply_smoothing(
        self,
        data: List[Dict[str, Any]],
        method: str = "SMA",
        window: int = 3
    ) -> List[Dict[str, Any]]:
        """
        独立的数据平滑方法

        Args:
            data: 输入数据
            method: 平滑方法 ("SMA", "EMA", "WMA")
            window: 平滑窗口大小

        Returns:
            平滑后的数据
        """
        return self.smooth_data(data, method, window)

    def normalize_data(
        self,
        data: List[Dict[str, Any]],
        method: str = "zscore"
    ) -> List[Dict[str, Any]]:
        """
        数据标准化处理

        Args:
            data: 输入数据
            method: 标准化方法 ("zscore", "minmax", "robust")

        Returns:
            标准化后的数据
        """
        if not data:
            return []

        if len(data) < 2:
            self.logger.warning("数据点数少于2个，跳过标准化处理")
            return data.copy()

        df = pd.DataFrame(data)
        df = df.sort_values('timestamp').reset_index(drop=True)
        values = df['value'].values

        if method == "zscore":
            # Z-score标准化：(x - mean) / std
            mean_val = np.mean(values)
            std_val = np.std(values)
            if std_val == 0:
                self.logger.warning("标准差为0，使用原始值")
                normalized_values = values
            else:
                normalized_values = (values - mean_val) / std_val

        elif method == "minmax":
            # Min-Max标准化：(x - min) / (max - min)
            min_val = np.min(values)
            max_val = np.max(values)
            if max_val == min_val:
                self.logger.warning("最大值等于最小值，使用原始值")
                normalized_values = values
            else:
                normalized_values = (values - min_val) / (max_val - min_val)

        elif method == "robust":
            # 鲁棒标准化：(x - median) / IQR
            median_val = np.median(values)
            Q1 = np.percentile(values, 25)
            Q3 = np.percentile(values, 75)
            IQR = Q3 - Q1
            if IQR == 0:
                self.logger.warning("IQR为0，使用原始值")
                normalized_values = values
            else:
                normalized_values = (values - median_val) / IQR

        else:
            raise ValueError(f"不支持的标准化方法: {method}")

        # 转换回原始格式
        result_data = []
        for i, row in df.iterrows():
            result_data.append({
                "timestamp": int(row['timestamp']),
                "value": float(normalized_values[i])
            })

        return result_data

    # 独立的数据预处理方法，用于为其他模块提供接口
    def handle_missing_values(
        self,
        data: List[Dict[str, Any]],
        method: str = "linear"
    ) -> List[Dict[str, Any]]:
        """
        独立的数据缺省处理方法

        Args:
            data: 输入数据
            method: 插值方法 ("linear", "cubic", "nearest")

        Returns:
            插值后的数据
        """
        return self.interpolate_missing_values(data, method)

    def remove_noise(
        self,
        data: List[Dict[str, Any]],
        method: str = "zscore",
        threshold: float = 3.0
    ) -> Tuple[List[Dict[str, Any]], List[int]]:
        """
        独立的噪声过滤方法

        Args:
            data: 输入数据
            method: 过滤方法 ("zscore", "iqr", "none")
            threshold: 过滤阈值

        Returns:
            Tuple[过滤后的数据, 噪声点的时间戳索引列表]
        """
        return self.filter_noise(data, method, threshold)

    def apply_smoothing(
        self,
        data: List[Dict[str, Any]],
        method: str = "SMA",
        window: int = 3
    ) -> List[Dict[str, Any]]:
        """
        独立的数据平滑方法

        Args:
            data: 输入数据
            method: 平滑方法 ("SMA", "EMA", "WMA")
            window: 平滑窗口大小

        Returns:
            平滑后的数据
        """
        return self.smooth_data(data, method, window)

    def denormalize_data(
        self,
        normalized_data: List[Dict[str, Any]],
        original_data: List[Dict[str, Any]],
        method: str = "zscore"
    ) -> List[Dict[str, Any]]:
        """
        数据反标准化处理

        Args:
            normalized_data: 标准化后的数据
            original_data: 原始数据（用于计算标准化参数）
            method: 标准化方法 ("zscore", "minmax", "robust")

        Returns:
            反标准化后的数据
        """
        if not normalized_data or not original_data or method == "none":
            return normalized_data.copy()

        # 计算原始数据的统计参数
        original_values = np.array([item["value"] for item in original_data])
        normalized_values = np.array([item["value"] for item in normalized_data])

        if method == "zscore":
            mean_val = np.mean(original_values)
            std_val = np.std(original_values)
            if std_val == 0:
                denormalized_values = normalized_values
            else:
                denormalized_values = normalized_values * std_val + mean_val

        elif method == "minmax":
            min_val = np.min(original_values)
            max_val = np.max(original_values)
            if max_val == min_val:
                denormalized_values = normalized_values
            else:
                denormalized_values = normalized_values * (max_val - min_val) + min_val

        elif method == "robust":
            median_val = np.median(original_values)
            Q1 = np.percentile(original_values, 25)
            Q3 = np.percentile(original_values, 75)
            IQR = Q3 - Q1
            if IQR == 0:
                denormalized_values = normalized_values
            else:
                denormalized_values = normalized_values * IQR + median_val

        else:
            raise ValueError(f"不支持的反标准化方法: {method}")

        # 转换回原始格式
        result_data = []
        for i, item in enumerate(normalized_data):
            result_data.append({
                "timestamp": item["timestamp"],
                "value": float(denormalized_values[i])
            })

        return result_data


# 便利函数，用于快速调用预处理功能
def preprocess_time_series_data(
    data: List[Dict[str, Any]],
    config: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    时间序列数据预处理的便利函数

    Args:
        data: 输入数据，格式为 [{"timestamp": int, "value": float}, ...]
        config: 预处理配置，包含以下可选参数：
            - interpolation_method: 插值方法 ("linear", "cubic", "nearest")
            - noise_filter_method: 噪声过滤方法 ("zscore", "iqr", "none")
            - noise_threshold: 噪声过滤阈值 (float)
            - smoothing_method: 平滑方法 ("SMA", "EMA", "WMA")
            - smoothing_window: 平滑窗口大小 (int)

    Returns:
        预处理后的数据，格式与输入相同
    """
    if config is None:
        config = {}

    # 默认配置
    default_config = {
        "interpolation_method": "linear",
        "noise_filter_method": "zscore",
        "noise_threshold": 3.0,
        "smoothing_method": "SMA",
        "smoothing_window": 3,
        "normalization_method": "zscore"
    }

    # 合并配置
    final_config = {**default_config, **config}

    # 创建预处理器并执行
    preprocessor = DataPreprocessor()
    return preprocessor.preprocess_data(data, **final_config)


def create_preprocessing_config(
    interpolation: str = "linear",
    noise_filter: str = "zscore",
    noise_threshold: float = 3.0,
    smoothing: str = "SMA",
    window: int = 3,
    normalization: str = "zscore"
) -> Dict[str, Any]:
    """
    创建预处理配置的便利函数

    Args:
        interpolation: 插值方法
        noise_filter: 噪声过滤方法
        noise_threshold: 噪声过滤阈值
        smoothing: 平滑方法
        window: 平滑窗口大小
        normalization: 标准化方法

    Returns:
        预处理配置字典
    """
    return {
        "interpolation_method": interpolation,
        "noise_filter_method": noise_filter,
        "noise_threshold": noise_threshold,
        "smoothing_method": smoothing,
        "smoothing_window": window,
        "normalization_method": normalization
    }


# 预定义的配置模板
PREPROCESSING_CONFIGS = {
    "default": create_preprocessing_config(),
    "conservative": create_preprocessing_config(
        interpolation="linear",
        noise_filter="iqr",
        noise_threshold=1.5,
        smoothing="SMA",
        window=5,
        normalization="robust"
    ),
    "aggressive": create_preprocessing_config(
        interpolation="cubic",
        noise_filter="zscore",
        noise_threshold=2.0,
        smoothing="EMA",
        window=7,
        normalization="zscore"
    ),
    "minimal": create_preprocessing_config(
        interpolation="linear",
        noise_filter="none",
        noise_threshold=3.0,
        smoothing="SMA",
        window=3,
        normalization="minmax"
    )
}


if __name__ == "__main__":
    # 使用示例
    import time

    # 创建示例数据
    base_time = int(time.time())
    sample_data = []

    for i in range(100):
        timestamp = base_time + i * 30  # 每30秒一个数据点
        value = 10 + 2 * np.sin(i * 0.1) + np.random.normal(0, 0.5)

        # 添加一些异常值
        if i in [20, 50, 80]:
            value += 10  # 异常高值
        elif i in [30, 60]:
            value -= 8   # 异常低值

        sample_data.append({"timestamp": timestamp, "value": value})

    # 人为制造一些缺失数据点
    sample_data = [d for i, d in enumerate(sample_data) if i not in [25, 26, 55, 56, 57]]

    print(f"原始数据点数: {len(sample_data)}")

    # 使用默认配置进行预处理
    processed_data = preprocess_time_series_data(sample_data)
    print(f"预处理后数据点数: {len(processed_data)}")

    # 使用保守配置进行预处理
    conservative_data = preprocess_time_series_data(sample_data, PREPROCESSING_CONFIGS["conservative"])
    print(f"保守配置预处理后数据点数: {len(conservative_data)}")

    # 使用激进配置进行预处理
    aggressive_data = preprocess_time_series_data(sample_data, PREPROCESSING_CONFIGS["aggressive"])
    print(f"激进配置预处理后数据点数: {len(aggressive_data)}")
