import requests
import logging
from datetime import datetime
import concurrent.futures
from threading import Lock
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import time as _time

class ThreadSafeCounter:
    """线程安全的计数器和状态管理"""
    def __init__(self):
        self._lock = Lock()
        self._consecutive_empty_count = 0
        self._early_stop = False

    def increment_empty_count(self):
        """增加空数据计数"""
        with self._lock:
            self._consecutive_empty_count += 1
            return self._consecutive_empty_count

    def reset_empty_count(self):
        """重置空数据计数"""
        with self._lock:
            self._consecutive_empty_count = 0

    def set_early_stop(self):
        """设置早停标志"""
        with self._lock:
            self._early_stop = True

    def should_early_stop(self):
        """检查是否应该早停"""
        with self._lock:
            return self._early_stop

    def get_empty_count(self):
        """获取当前空数据计数"""
        with self._lock:
            return self._consecutive_empty_count

def create_http_session():
    """创建带有重试机制的HTTP会话"""
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )

    # 配置适配器
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=20
    )

    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session

def query_metrics(start_time, end_time, vregion, query, block_cache, log=False):
    """
    并行查询指定时间段的数据，返回合并后的数据列表
    """
    headers = {
        "x-openapi-token": "eyJuYW1lIjoiZXV0b3BpYSIsInN1YiI6Im1vZGVsX3Rvb2wiLCJpYXQiOjE3NDMwNjA4MjR9"
    }

    # 创建线程安全的计数器和HTTP会话
    counter = ThreadSafeCounter()
    http_session = create_http_session()

    one_hour = 3600
    time_segments = []
    
    # 计算第一个整点
    first_full_hour = ((start_time // one_hour) + 1) * one_hour
    
    t = start_time
    # 添加第一个非整点段（如果存在）
    if t < first_full_hour and first_full_hour < end_time:
        time_segments.append((t, first_full_hour))
        t = first_full_hour
    
    # 添加整点到整点的每小时段
    while t + one_hour <= end_time:
        time_segments.append((t, t + one_hour))
        t += one_hour
    
    # 添加最后一个非整点段（如果存在）
    if t < end_time:
        time_segments.append((t, end_time))
    
    def query_single_segment(segment_info):
        """查询单个时间段的数据"""
        segment_index, (part_start, part_end) = segment_info
        body = {
            "start_time": part_start,
            "end_time": part_end,
            "vregion": vregion,
            "query": query,
            "retry_num": 5,
            "block_cache": block_cache,
            "cache_timeout": 3600,
        }

        try:
            if log:
                logging.info(f"开始查询数据段 {segment_index}: {datetime.fromtimestamp(part_start)} ~ {datetime.fromtimestamp(part_end)}")

            # 使用共享的HTTP会话，添加超时配置
            response = http_session.post(
                "https://eutopia.bytedance.net/api/data_center/query_metrics",
                headers=headers,
                json=body,
                timeout=(5, 30)  # 连接超时5秒，读取超时30秒
            )
            response.raise_for_status()
            part_data = response.json().get("data", [])

            if log:
                logging.info(f"完成查询数据段 {segment_index}: {datetime.fromtimestamp(part_start)} ~ {datetime.fromtimestamp(part_end)}")

            return segment_index, part_data

        except requests.exceptions.RequestException as e:
            logging.error(f"时间段 {segment_index} ({datetime.fromtimestamp(part_start)} ~ {datetime.fromtimestamp(part_end)}) 查询失败: {e}")
            raise
        except Exception as e:
            logging.error(f"时间段 {segment_index} 处理异常: {e}")
            raise
    
    # 分批并发查询：批间串行（倒序），批内并发
    segment_results = {}

    # 倒序索引列表
    reverse_indices = list(range(len(time_segments) - 1, -1, -1))
    
    # 分批参数
    batch_size = 5  # 每批处理的时间片数量
    max_workers = 5  # 每批内的并发数
    
    # 将倒序索引分批
    batches = []
    for i in range(0, len(reverse_indices), batch_size):
        batch = reverse_indices[i:i + batch_size]
        batches.append(batch)
    
    if log:
        logging.info(f"将 {len(time_segments)} 个时间段分为 {len(batches)} 批，每批最多 {batch_size} 个时间段")
    
    # 分批处理
    for batch_idx, batch_indices in enumerate(batches):
        if counter.should_early_stop():
            break

        if log:
            logging.info(f"开始处理第 {batch_idx + 1} 批，包含时间段索引: {batch_indices}")

        # 批内并发查询
        batch_results = {}
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交当前批次的所有任务
            futures = {}
            for i in batch_indices:
                segment = time_segments[i]
                future = executor.submit(query_single_segment, (i, segment))
                futures[future] = i

            # 收集当前批次的结果
            for future in concurrent.futures.as_completed(futures):
                i = futures[future]
                try:
                    segment_index, part_data = future.result()
                    batch_results[segment_index] = part_data
                except Exception as e:
                    logging.error(f"时间段 {i} 查询异常: {e}")
                    batch_results[i] = []  # 失败时填空
        
        # 将批次结果合并到总结果中
        segment_results.update(batch_results)

        # 按批次内的倒序检查早停条件
        for i in sorted(batch_indices, reverse=True):  # 批内也按倒序检查
            part_data = batch_results.get(i, [])

            if not part_data:  # 当前时间片为空
                empty_count = counter.increment_empty_count()
                if log:
                    logging.info(f"时间段 {i} 查询结果为空，连续空数据段数: {empty_count}")

                if empty_count >= 3:
                    if log:
                        logging.info(f"连续 {empty_count} 个时间段为空，触发早停机制，停止查询更早的时间段")
                    counter.set_early_stop()
                    break
            else:
                # 重置连续空数据计数
                counter.reset_empty_count()

        # 批次间休息，降低系统压力
        if not counter.should_early_stop() and batch_idx < len(batches) - 1:
            _time.sleep(0.1)
    
    # 检查并汇总空数据时间段（只统计实际查询的时间段）
    empty_segments = []
    queried_segments = list(segment_results.keys())
    
    for i in sorted(queried_segments):
        if not segment_results[i]:  # 数据为空
            empty_segments.append(i)
    
    # 记录早停信息
    if counter.should_early_stop():
        total_segments = len(time_segments)
        queried_count = len(queried_segments)
        skipped_count = total_segments - queried_count
        if log:
            logging.info(f"早停机制生效：总时间段 {total_segments} 个，实际查询 {queried_count} 个，跳过 {skipped_count} 个早期时间段")
    
    # 拼接相邻的空时间段并打印日志
    if empty_segments:
        merged_empty_ranges = []
        start_idx = empty_segments[0]
        end_idx = empty_segments[0]
        
        for i in range(1, len(empty_segments)):
            if empty_segments[i] == end_idx + 1:  # 相邻时间段
                end_idx = empty_segments[i]
            else:
                # 添加当前连续段到结果
                if start_idx == end_idx:
                    start_time_str = datetime.fromtimestamp(time_segments[start_idx][0])
                    end_time_str = datetime.fromtimestamp(time_segments[start_idx][1])
                    merged_empty_ranges.append(f"{start_time_str} ~ {end_time_str}")
                else:
                    start_time_str = datetime.fromtimestamp(time_segments[start_idx][0])
                    end_time_str = datetime.fromtimestamp(time_segments[end_idx][1])
                    merged_empty_ranges.append(f"{start_time_str} ~ {end_time_str}")
                
                # 开始新的连续段
                start_idx = empty_segments[i]
                end_idx = empty_segments[i]
        
        # 添加最后一个连续段
        if start_idx == end_idx:
            start_time_str = datetime.fromtimestamp(time_segments[start_idx][0])
            end_time_str = datetime.fromtimestamp(time_segments[start_idx][1])
            merged_empty_ranges.append(f"{start_time_str} ~ {end_time_str}")
        else:
            start_time_str = datetime.fromtimestamp(time_segments[start_idx][0])
            end_time_str = datetime.fromtimestamp(time_segments[end_idx][1])
            merged_empty_ranges.append(f"{start_time_str} ~ {end_time_str}")
        if log:
            logging.warning(f"以下时间段查询结果为空: {'; '.join(merged_empty_ranges)}")
    
    # 按时间顺序合并数据
    results = {}
    for i in sorted(segment_results.keys()):
        part_data = segment_results[i]
        for item in part_data:
            key = tuple(sorted(item["dimensions"].items()))
            if key not in results:
                results[key] = {
                    "dimensions": item["dimensions"],
                    "values": []
                }
            # 按时间顺序添加values，确保时间段的顺序性
            results[key]["values"].extend(item.get("values", []))

    # 关闭HTTP会话，释放资源
    try:
        http_session.close()
    except Exception as e:
        if log:
            logging.warning(f"关闭HTTP会话时出现异常: {e}")

    return list(results.values())