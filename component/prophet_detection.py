import time
import numpy as np
from datetime import datetime, timedelta, timezone
import pandas as pd
from prophet import Prophet
from prophet.serialize import model_to_json, model_from_json
import logging
import plotly.graph_objects as go
import json
from component.data_preprocessing import DataPreprocessor

# 定义UTC+8时区
UTC_PLUS_8 = timezone(timedelta(hours=8))

def preprocess_history_data(
    history_data,
    noise_filter_method="iqr",
    noise_threshold=3.0,
    smoothing_window=3,
    smoothing_method="mean",
    create_noise_plot=False
):
    """
    对历史数据进行预处理：噪声过滤 + 平滑处理

    Args:
        history_data: 历史数据列表，格式为 [{"timestamp": int, "value": float}, ...]
        noise_filter_method: 噪声过滤方法 ("zscore", "iqr", "none")
        noise_threshold: 噪声过滤阈值
        smoothing_window: 平滑窗口大小
        smoothing_method: 平滑方法 ("mean", "median")
        create_noise_plot: 是否创建噪声过滤可视化图表

    Returns:
        tuple: (
            processed_data: 处理后的历史数据 (pandas.Series),
            outlier_indices: 异常值的时间戳索引列表,
            noise_filter_plot: 噪声过滤图表HTML (如果create_noise_plot=True)
        )
    """
    logging.info("开始历史数据预处理")

    # 步骤1: 噪声过滤
    logging.info(f"开始噪声过滤，方法: {noise_filter_method}, 阈值: {noise_threshold}")
    preprocessor = DataPreprocessor()

    filtered_data, outlier_timestamps, noise_filter_plot = preprocessor.filter_noise(
        history_data,
        method=noise_filter_method,
        threshold=noise_threshold,
        create_plot=create_noise_plot
    )

    logging.info(f"噪声过滤完成，原始数据点数: {len(history_data)}, 过滤后数据点数: {len(filtered_data)}, 异常点数: {len(outlier_timestamps)}")

    # 步骤2: 数据平滑处理
    logging.info(f"开始数据平滑处理，窗口大小: {smoothing_window}, 方法: {smoothing_method}")

    # 将过滤后的数据转换为时间序列格式
    filtered_times = [datetime.fromtimestamp(item["timestamp"]) for item in filtered_data]
    filtered_values = np.array([item["value"] for item in filtered_data])

    # 对过滤后的数据进行平滑处理
    smoothed_values = smooth_series(filtered_values, window=smoothing_window, method=smoothing_method)

    # 创建平滑后的数据Series
    processed_data = pd.Series(smoothed_values, index=filtered_times)

    logging.info(f"数据平滑处理完成，最终数据点数: {len(processed_data)}")

    return processed_data, outlier_timestamps, noise_filter_plot

def train_prophet_model_full(all_history, debug_other_image=False):
    """
    全量训练Prophet模型并获取历史数据分量

    Args:
        all_history: 历史数据列表
        debug_other_image: 是否创建调试图表

    Returns:
        tuple: (model, df_history_smoothed, trend, daily, resid, outlier_timestamps, noise_filter_plot)
    """
    logging.info("全量训练Prophet模型")

    # 使用封装的预处理函数对历史数据进行噪声过滤和平滑处理
    df_history_smoothed, outlier_timestamps, noise_filter_plot = preprocess_history_data(
        all_history,
        noise_filter_method="iqr",
        noise_threshold=3.0,
        smoothing_window=3,
        smoothing_method="mean",
        create_noise_plot=debug_other_image
    )

    # 训练Prophet模型
    model = prophet_decompose(df_history_smoothed)

    # 获取历史数据的分量（全量训练时需要重新计算）
    logging.info("使用Prophet模型分解历史数据")
    predict_history_start_time = time.time()

    future = pd.DataFrame({"ds": df_history_smoothed.index})
    forecast = model.predict(future)
    trend = pd.Series(forecast["trend"].values, index=df_history_smoothed.index)
    daily = pd.Series(forecast["daily"].values, index=df_history_smoothed.index)
    resid = df_history_smoothed - (trend + daily)

    predict_history_end_time = time.time()
    logging.info(f"Prophet模型分解历史数据完毕，耗时 {predict_history_end_time - predict_history_start_time:.2f} 秒")

    return model, df_history_smoothed, trend, daily, resid, outlier_timestamps, noise_filter_plot

def use_pretrained_prophet_model(all_history, model_data, debug_other_image=False):
    """
    使用预训练Prophet模型并获取历史数据分量

    Args:
        all_history: 历史数据列表
        model_data: 预训练模型数据
        debug_other_image: 是否创建调试图表

    Returns:
        tuple: (model, df_history_smoothed, trend, daily, resid, outlier_timestamps, noise_filter_plot)
    """
    logging.info("使用预训练模型进行预测")

    # 加载预训练模型
    model, _, precomputed_components = load_prophet_model(model_data)

    # 检查预训练模型中是否包含异常值索引和噪声过滤图像
    if "outlier_indices" in model_data and "noise_filter_plot" in model_data:
        logging.info("使用预训练模型中存储的异常值索引和噪声过滤图像")
        outlier_timestamps = model_data["outlier_indices"]
        noise_filter_plot = model_data["noise_filter_plot"] if debug_other_image else ""

        # 重建处理后的历史数据Series（跳过噪声过滤图像生成）
        df_history_smoothed, _, _ = preprocess_history_data(
            all_history,
            noise_filter_method="iqr",
            noise_threshold=3.0,
            smoothing_window=3,
            smoothing_method="mean",
            create_noise_plot=False  # 不重新生成图像，使用存储的
        )
    else:
        logging.warning("预训练模型中缺少异常值索引或噪声过滤图像，重新计算")
        # 如果预训练模型中没有这些数据，重新计算
        df_history_smoothed, outlier_timestamps, noise_filter_plot = preprocess_history_data(
            all_history,
            noise_filter_method="iqr",
            noise_threshold=3.0,
            smoothing_window=3,
            smoothing_method="mean",
            create_noise_plot=debug_other_image
        )

    # 获取历史数据的分量
    predict_history_start_time = time.time()

    # 如果有预计算的分量，直接使用预计算结果
    if precomputed_components is not None:
        logging.info("使用预计算历史数据分量，未覆盖的时间段保持为空")
        # 将预计算的时间戳转换为datetime并创建Series
        precomputed_timestamps = [datetime.fromtimestamp(ts) for ts in precomputed_components["timestamps"]]
        precomputed_trend = pd.Series(precomputed_components["trend"], index=precomputed_timestamps)
        precomputed_daily = pd.Series(precomputed_components["daily"], index=precomputed_timestamps)
        precomputed_resid = pd.Series(precomputed_components["resid"], index=precomputed_timestamps)

        # 使用pandas的reindex方法高效填充数据，未覆盖的时间保持为NaN
        trend = precomputed_trend.reindex(df_history_smoothed.index)
        daily = precomputed_daily.reindex(df_history_smoothed.index)
        resid = precomputed_resid.reindex(df_history_smoothed.index)
    else:
        # 没有预计算分量，使用模型predict获取历史数据的分量
        logging.info("没有预计算历史数据分量，使用Prophet模型进行预测")
        future = pd.DataFrame({"ds": df_history_smoothed.index})
        forecast = model.predict(future)
        trend = pd.Series(forecast["trend"].values, index=df_history_smoothed.index)
        daily = pd.Series(forecast["daily"].values, index=df_history_smoothed.index)
        resid = df_history_smoothed - (trend + daily)

    predict_history_end_time = time.time()
    logging.info(f"Prophet模型分解历史数据完毕，耗时 {predict_history_end_time - predict_history_start_time:.2f} 秒")

    return model, df_history_smoothed, trend, daily, resid, outlier_timestamps, noise_filter_plot

def calculate_thresholds_and_detect_anomalies(
    today_times, today_values_smoothed, df_history_smoothed, resid,
    min_anomaly_run=4, sigma_soft=3, sigma_hard=5, sigma_diff=5
):
    """
    计算阈值并进行异常检测

    Args:
        today_times: 今日时间列表
        today_values_smoothed: 今日平滑后的数值
        df_history_smoothed: 历史平滑后的数据Series
        resid: 历史残差数据
        min_anomaly_run: 最小异常连续点数
        sigma_soft: 软阈值倍数
        sigma_hard: 硬阈值倍数
        sigma_diff: 差分阈值倍数

    Returns:
        tuple: (is_anomaly, anomaly_times, final_anomaly_idx)
    """
    logging.info("开始阈值计算和异常检测")

    day_len = len(today_times)

    # 计算今日残差
    # 这里需要从外部传入trend_pred和daily_pred，暂时先计算
    # 实际使用时需要调整参数

    # 残差阈值计算
    # 过滤掉NaN值
    valid_resid = resid.dropna()

    if len(valid_resid) > 0:
        # 计算全部残差的统计特征
        resid_mean = valid_resid.mean()
        resid_std = valid_resid.std()

        # 为每个检测点使用相同的阈值
        means = np.full(day_len, resid_mean)
        stds = np.full(day_len, resid_std)
        upper = means + sigma_soft * stds
        lower = means - sigma_soft * stds
        hard_upper = means + sigma_hard * stds
        hard_lower = means - sigma_hard * stds
    else:
        # 如果没有有效残差数据，使用默认值
        logging.warning("没有有效的残差数据，使用默认阈值")
        means = np.zeros(day_len)
        stds = np.ones(day_len)
        upper = sigma_soft * stds
        lower = -sigma_soft * stds
        hard_upper = sigma_hard * stds
        hard_lower = -sigma_hard * stds

    # 阈值平滑处理
    smooth_window = 5
    smooth_upper = np.array(smooth_series(upper, window=smooth_window, method="mean"))
    smooth_lower = np.array(smooth_series(lower, window=smooth_window, method="mean"))
    smooth_hard_upper = np.array(smooth_series(hard_upper, window=smooth_window, method="mean"))
    smooth_hard_lower = np.array(smooth_series(hard_lower, window=smooth_window, method="mean"))

    # 差分异常检测：使用平滑后的原始数据而不是残差
    # 计算今日平滑数据的差分
    today_diff = np.diff(today_values_smoothed, prepend=np.nan)

    # 计算历史平滑数据的差分
    history_diff_vals = df_history_smoothed.diff().dropna().values

    if len(history_diff_vals) > 0:
        # 计算历史差分的统计特征
        diff_mean = np.mean(history_diff_vals)
        diff_std = np.std(history_diff_vals)
    else:
        # 如果没有足够的历史数据，使用默认值
        diff_mean = 0
        diff_std = 1

    # 为每个检测点使用相同的差分阈值
    diff_means = np.full(len(today_diff), diff_mean)
    diff_stds = np.full(len(today_diff), diff_std)
    diff_upper = diff_means + sigma_diff * diff_stds
    diff_lower = diff_means - sigma_diff * diff_stds

    # 差分阈值平滑处理
    smooth_diff_upper = np.array(smooth_series(diff_upper, window=smooth_window, method="mean"))
    smooth_diff_lower = np.array(smooth_series(diff_lower, window=smooth_window, method="mean"))

    diff_anomaly_idx = [i for i, v in enumerate(today_diff) if v > smooth_diff_upper[i] or v < smooth_diff_lower[i]]

    return {
        'smooth_upper': smooth_upper,
        'smooth_lower': smooth_lower,
        'smooth_hard_upper': smooth_hard_upper,
        'smooth_hard_lower': smooth_hard_lower,
        'smooth_diff_upper': smooth_diff_upper,
        'smooth_diff_lower': smooth_diff_lower,
        'today_diff': today_diff,
        'diff_anomaly_idx': diff_anomaly_idx
    }

def detect_anomalies_with_thresholds(
    today_times, today_resid, threshold_data, min_anomaly_run=4
):
    """
    使用计算好的阈值进行异常检测

    Args:
        today_times: 今日时间列表
        today_resid: 今日残差
        threshold_data: 阈值数据字典
        min_anomaly_run: 最小异常连续点数

    Returns:
        tuple: (is_anomaly, anomaly_times, final_anomaly_idx)
    """
    # 1. 超过5σ直接异常
    hard_anomaly_idx = [i for i, v in enumerate(today_resid)
                        if v > threshold_data['smooth_hard_upper'][i] or v < threshold_data['smooth_hard_lower'][i]]

    # 2. 3σ外连续N个点，且这些点中有差分异常，则这些点都算异常
    soft_idx = [i for i, v in enumerate(today_resid)
                if (v > threshold_data['smooth_upper'][i] or v < threshold_data['smooth_lower'][i])]

    soft_runs = []
    run = []
    for idx in soft_idx:
        if run and idx == run[-1] + 1:
            run.append(idx)
        else:
            if len(run) >= min_anomaly_run:
                soft_runs.append(run)
            run = [idx]
    if len(run) >= min_anomaly_run:
        soft_runs.append(run)

    soft_anomaly_idx = []
    for run in soft_runs:
        if any(i in threshold_data['diff_anomaly_idx'] for i in run):
            soft_anomaly_idx.extend(run)

    final_anomaly_idx = sorted(set(hard_anomaly_idx) | set(soft_anomaly_idx))
    anomaly_times = [today_times[i] for i in final_anomaly_idx]
    is_anomaly = len(final_anomaly_idx) > 0

    return is_anomaly, anomaly_times, final_anomaly_idx

def create_debug_plots(
    all_history_times, all_history_values, today_times, today_values_smoothed,
    df_original_history_smoothed, trend_pred, daily_pred, resid, today_resid,
    threshold_data, final_anomaly_idx, outlier_timestamps, noise_filter_plot,
    debug_main_image=False, debug_all_image=False, debug_other_image=False, debug_resid_image=False
):
    """
    创建所有调试图表

    Args:
        all_history_times: 历史时间列表
        all_history_values: 历史数值列表
        today_times: 今日时间列表
        today_values_smoothed: 今日平滑后数值
        df_original_history_smoothed: 原始历史平滑数据
        trend_pred: 趋势预测
        daily_pred: 日周期预测
        resid: 历史残差
        today_resid: 今日残差
        threshold_data: 阈值数据
        final_anomaly_idx: 最终异常索引
        outlier_timestamps: 异常值时间戳
        noise_filter_plot: 噪声过滤图表
        debug_main_image: 是否创建主图
        debug_all_image: 是否创建全量图
        debug_other_image: 是否创建其他图表
        debug_resid_image: 是否创建残差图

    Returns:
        tuple: (img_main, img_all_data, img_resid_data, debug_other_images)
    """
    logging.info("开始创建调试图表")

    # 初始化返回值
    img_main = ""
    img_all_data = ""
    img_resid_data = ""
    debug_other_images = {
        "trend": "",
        "daily": "",
        "diff_main": "",
        "diff_all": "",
        "noise": noise_filter_plot if debug_other_image else "",
    }

    # 时区转换
    history_times_utc8 = convert_to_utc_plus_8(all_history_times)
    today_times_utc8 = convert_to_utc_plus_8(today_times)

    # 主图 - 今日数据异常检测结果
    if debug_main_image:
        logging.info("创建主图")
        img_main = _create_main_plot(
            today_times_utc8, today_values_smoothed, trend_pred, daily_pred,
            today_resid, threshold_data, final_anomaly_idx
        )

    # 历史全量图
    if debug_all_image:
        logging.info("创建历史全量图")
        img_all_data = _create_all_data_plot(
            history_times_utc8, df_original_history_smoothed, today_times_utc8,
            today_values_smoothed, final_anomaly_idx, outlier_timestamps
        )

    # 残差图
    if debug_resid_image:
        logging.info("创建残差图")
        img_resid_data = _create_residual_plot(
            history_times_utc8, resid, today_times_utc8, today_resid,
            threshold_data, final_anomaly_idx
        )

    # 其他调试图表
    if debug_other_image:
        logging.info("创建其他调试图表")
        debug_other_images.update(_create_other_debug_plots(
            history_times_utc8, df_original_history_smoothed, today_times_utc8,
            today_values_smoothed, trend_pred, daily_pred, threshold_data,
            final_anomaly_idx
        ))

    return img_main, img_all_data, img_resid_data, debug_other_images

def _create_main_plot(today_times_utc8, today_values_smoothed, trend_pred, daily_pred, today_resid, threshold_data, final_anomaly_idx):
    """创建主图 - 今日数据异常检测结果"""
    # 这里是原来主图创建的逻辑，暂时返回空字符串
    # 实际实现时需要将原来的主图创建代码移到这里
    return ""

def _create_all_data_plot(history_times_utc8, df_original_history_smoothed, today_times_utc8, today_values_smoothed, final_anomaly_idx, outlier_timestamps):
    """创建历史全量图"""
    # 这里是原来历史全量图创建的逻辑，暂时返回空字符串
    # 实际实现时需要将原来的历史全量图创建代码移到这里
    return ""

def _create_residual_plot(history_times_utc8, resid, today_times_utc8, today_resid, threshold_data, final_anomaly_idx):
    """创建残差图"""
    # 这里是原来残差图创建的逻辑，暂时返回空字符串
    # 实际实现时需要将原来的残差图创建代码移到这里
    return ""

def _create_other_debug_plots(history_times_utc8, df_original_history_smoothed, today_times_utc8, today_values_smoothed, trend_pred, daily_pred, threshold_data, final_anomaly_idx):
    """创建其他调试图表"""
    # 这里是原来其他调试图表创建的逻辑，暂时返回空字典
    # 实际实现时需要将原来的其他图表创建代码移到这里
    return {
        "trend": "",
        "daily": "",
        "diff_main": "",
        "diff_all": "",
    }

def convert_to_utc_plus_8(dt_list):
    """将UTC时间列表转换为UTC+8时间列表，仅用于画图显示"""
    return [dt.replace(tzinfo=timezone.utc).astimezone(UTC_PLUS_8) for dt in dt_list]

def smooth_series(values, window=3, method="mean"):
    s = pd.Series(values)
    if method == "mean":
        return s.rolling(window, center=True, min_periods=1).mean().tolist()
    elif method == "median":
        return s.rolling(window, center=True, min_periods=1).median().tolist()
    else:
        raise ValueError("method must be 'mean' or 'median'")

def prophet_decompose(series, freq="D"):
    """用Prophet训练模型，只返回训练好的模型"""
    df = pd.DataFrame({"ds": series.index, "y": series.values})
    model = Prophet(
        yearly_seasonality=False,
        weekly_seasonality=False,
        daily_seasonality=True,
        interval_width=0.9,
        changepoint_prior_scale=0.00001,
        seasonality_prior_scale=50.0,
    )
    model.fit(df)
    return model

def prophet_forecast(model, future_index, component="yhat"):
    df_future = pd.DataFrame({"ds": future_index})
    forecast = model.predict(df_future)
    return forecast[component].values

def save_prophet_model(model, last_training_timestamp, training_series=None, outlier_indices=None, noise_filter_plot=None):
    """
    将Prophet模型序列化为JSON格式，并包含训练数据的最后时间戳
    如果提供了training_series，还会预计算并保存历史数据的分量

    Args:
        model: 训练好的Prophet模型
        last_training_timestamp: 训练数据的最后时间戳
        training_series: 可选，训练数据的Series，用于预计算分量
        outlier_indices: 可选，异常值的时间戳索引列表
        noise_filter_plot: 可选，噪声过滤图表HTML

    Returns:
        dict: 包含模型JSON、最后时间戳、预计算分量、异常值索引和噪声过滤图表的字典
    """
    model_json = model_to_json(model)
    result = {
        "model_json": model_json,
        "last_training_timestamp": last_training_timestamp
    }

    # 如果提供了训练数据，预计算分量
    if training_series is not None:
        logging.info("开始预计算历史数据分量")
        precompute_start_time = time.time()

        # 使用模型predict获取历史数据的分量
        future = pd.DataFrame({"ds": training_series.index})
        forecast = model.predict(future)

        # 创建带时间索引的Series
        trend_series = pd.Series(forecast["trend"].values, index=training_series.index)
        daily_series = pd.Series(forecast["daily"].values, index=training_series.index)
        resid_series = training_series - (trend_series + daily_series)

        # 转换为可序列化的格式（时间戳和值的对应关系）
        timestamps = [int(ts.timestamp()) for ts in training_series.index]

        # 保存预计算的分量
        result["precomputed_components"] = {
            "timestamps": timestamps,
            "trend": trend_series.values.tolist(),
            "daily": daily_series.values.tolist(),
            "resid": resid_series.values.tolist()
        }

        precompute_end_time = time.time()
        logging.info(f"历史数据分量预计算完成，耗时 {precompute_end_time - precompute_start_time:.2f} 秒")

    # 保存异常值索引和噪声过滤图表
    if outlier_indices is not None:
        result["outlier_indices"] = outlier_indices
        logging.info(f"保存异常值索引，异常点数: {len(outlier_indices)}")

    if noise_filter_plot is not None:
        result["noise_filter_plot"] = noise_filter_plot
        logging.info(f"保存噪声过滤图表，HTML长度: {len(noise_filter_plot)} 字符")

    return result

def load_prophet_model(model_data):
    """
    从JSON格式反序列化Prophet模型

    Args:
        model_data: 包含模型JSON和最后时间戳的字典

    Returns:
        tuple: (model, last_training_timestamp, precomputed_components)
        其中precomputed_components为None（如果没有预计算分量）或包含预计算分量的字典
    """
    model = model_from_json(model_data["model_json"])
    last_training_timestamp = model_data["last_training_timestamp"]
    precomputed_components = model_data.get("precomputed_components", None)
    return model, last_training_timestamp, precomputed_components

def prophet_anomaly_detection(
    all_history, today, start_time, debug_main_image = False, debug_all_image = False, debug_other_image = False, debug_resid_image = False, min_anomaly_run=4, sigma_soft=3, sigma_hard=5, sigma_diff=5, model_data=None, last_training_timestamp=None, time_window_minutes=60
):
    """
    Prophet异常检测主函数，使用模块化的方法
    """
    all_history_times = [datetime.fromtimestamp(item["timestamp"]) for item in all_history]
    all_history_values = np.array([item["value"] for item in all_history])
    today_times = [datetime.fromtimestamp(item["timestamp"]) for item in today]
    today_values = np.array([item["value"] for item in today])

    df_history = pd.DataFrame({"timestamp": all_history_times, "value": all_history_values}).set_index("timestamp")

    model_start_time = time.time()
    debug_mode = True

    # 模块1: 训练和分解部分（包括历史数据分量获取）
    if debug_mode:
        # 调试模式：强制使用全量训练
        model, df_history_smoothed, trend, daily, resid, outlier_timestamps, noise_filter_plot = train_prophet_model_full(
            all_history, debug_other_image
        )
    elif model_data is not None and last_training_timestamp is not None:
        # 使用预训练模型
        model, df_history_smoothed, trend, daily, resid, outlier_timestamps, noise_filter_plot = use_pretrained_prophet_model(
            all_history, model_data, debug_other_image
        )
    else:
        # 全量训练
        model, df_history_smoothed, trend, daily, resid, outlier_timestamps, noise_filter_plot = train_prophet_model_full(
            all_history, debug_other_image
        )

    # 今日数据平滑处理
    day_len = len(today)
    logging.info("开始今日数据平滑处理")
    today_values_smoothed = np.array(smooth_series(today_values, window=3, method="mean"))

    # 对原始历史数据也进行平滑处理（用于绘图显示）
    smoothed_original_history_values = smooth_series(all_history_values, window=3, method="mean")
    df_original_history_smoothed = pd.Series(smoothed_original_history_values, index=df_history.index)

    model_end_time = time.time()
    logging.info(f"Prophet模型训练和分解完毕，耗时 {model_end_time - model_start_time:.2f} 秒")

    prophet_start_time = time.time()
    trend_pred = pd.Series(
        prophet_forecast(model, today_times, component="trend"), index=today_times
    )
    daily_pred = pd.Series(
        prophet_forecast(model, today_times, component="daily"), index=today_times
    )
    prophet_end_time = time.time()
    logging.info(f"Prophet预测完成，耗时 {prophet_end_time - prophet_start_time:.2f} 秒")

    today_resid = today_values_smoothed - (trend_pred.values + daily_pred.values)

    # 模块2: 阈值计算和异常检测
    resid_start_time = time.time()

    # 计算阈值
    threshold_data = calculate_thresholds_and_detect_anomalies(
        today_times, today_values_smoothed, df_history_smoothed, resid,
        min_anomaly_run, sigma_soft, sigma_hard, sigma_diff
    )

    # 进行异常检测
    is_anomaly, anomaly_times, final_anomaly_idx = detect_anomalies_with_thresholds(
        today_times, today_resid, threshold_data, min_anomaly_run
    )

    resid_end_time = time.time()
    logging.info(f"残差异常检测完成，耗时 {resid_end_time - resid_start_time:.2f} 秒")

    # 模块3: 绘图部分
    draw_start_time = time.time()

    img_main, img_all_data, img_resid_data, debug_other_images = create_debug_plots(
        all_history_times, all_history_values, today_times, today_values_smoothed,
        df_original_history_smoothed, trend_pred, daily_pred, resid, today_resid,
        threshold_data, final_anomaly_idx, outlier_timestamps, noise_filter_plot,
        debug_main_image, debug_all_image, debug_other_image, debug_resid_image
    )

    draw_end_time = time.time()
    logging.info(f"绘图完成，耗时 {draw_end_time - draw_start_time:.2f} 秒")

    return (
        is_anomaly,
        anomaly_times,
        img_main,
        img_all_data,
        img_resid_data,
        debug_other_images,
    )

    # 初始化图像变量
    img_main = ""
    img_all_data = ""
    img_resid_data = ""
    img_trend = ""
    img_daily = ""
    img_diff_main = ""
    img_diff_all = ""

    if debug_main_image:
        # 1. 异常检测主图
        fig_main = go.Figure()

        # 转换时间为UTC+8用于显示
        today_times_utc8 = convert_to_utc_plus_8(today_times)

        # 添加5σ范围（浅灰色）
        upper_5sigma = trend_pred.values + daily_pred.values + smooth_hard_upper
        lower_5sigma = trend_pred.values + daily_pred.values + smooth_hard_lower
        fig_main.add_trace(go.Scatter(
            x=today_times_utc8,
            y=upper_5sigma,
            mode='lines',
            line=dict(color="rgba(0,0,0,0)", width=0),
            showlegend=False
        ))
        fig_main.add_trace(go.Scatter(
            x=today_times_utc8,
            y=lower_5sigma,
            mode='lines',
            line=dict(color="rgba(0,0,0,0)", width=0),
            fill='tonexty',
            fillcolor='rgba(220,220,220,0.7)',
            showlegend=False
        ))

        # 添加3σ范围（更浅的灰色）
        upper_3sigma = trend_pred.values + daily_pred.values + smooth_upper
        lower_3sigma = trend_pred.values + daily_pred.values + smooth_lower
        fig_main.add_trace(go.Scatter(
            x=today_times_utc8,
            y=upper_3sigma,
            mode='lines',
            line=dict(color="rgba(0,0,0,0)", width=0),
            showlegend=False
        ))
        fig_main.add_trace(go.Scatter(
            x=today_times_utc8,
            y=lower_3sigma,
            mode='lines',
            line=dict(color="rgba(0,0,0,0)", width=0),
            fill='tonexty',
            fillcolor='rgba(245,245,245,0.6)',
            showlegend=False
        ))

        # 添加今日数据线（柔和蓝色折线，使用平滑后的数据）
        fig_main.add_trace(go.Scatter(
            x=today_times_utc8,
            y=today_values_smoothed,
            mode='lines',
            name="Today",
            line=dict(color="#4A90E2", width=2),
            showlegend=False,
            hovertemplate='<b>今日数据</b><br>' +
                         '时间: %{x|%m-%d %H:%M}<br>' +
                         '值: %{y:.2f}<br>' +
                         '<extra></extra>'
        ))

        # 添加异常点（橙红色圆点，使用平滑后的数据）
        if final_anomaly_idx:
            fig_main.add_trace(go.Scatter(
                x=[today_times_utc8[i] for i in final_anomaly_idx],
                y=today_values_smoothed[final_anomaly_idx],
                mode='markers',
                marker=dict(color="#FF6B6B", size=8),
                showlegend=False,
                hovertemplate='<b>异常点</b><br>' +
                             '时间: %{x|%m-%d %H:%M}<br>' +
                             '值: %{y:.2f}<br>' +
                             '<extra></extra>'
            ))

        fig_main.update_layout(
            margin=dict(l=0, r=0, t=0, b=0),
            showlegend=False,
            plot_bgcolor='white',
            paper_bgcolor='white',
            xaxis=dict(
                title=None,
                tickformat="%H:%M",
                showline=False,
                showgrid=False,
                zeroline=False
            ),
            yaxis=dict(
                title=None,
                showline=False,
                showgrid=False,
                zeroline=False
            )
        )

        img_main = fig_main.to_html(
            include_plotlyjs='inline',
            config={'displayModeBar': False}
        )

    if debug_all_image:
        # 2. 数据全量图
        fig_all = go.Figure()

        # 转换历史数据时间为UTC+8用于显示
        history_times_utc8 = convert_to_utc_plus_8(df_history.index.tolist())
        today_times_utc8_all = convert_to_utc_plus_8(today_times)

        # 添加历史数据线（使用原始平滑后的数据，包含异常值）
        fig_all.add_trace(go.Scatter(
            x=history_times_utc8,
            y=df_original_history_smoothed,
            mode='lines',
            name="History (All)",
            line=dict(color="#4A90E2", width=1),
            showlegend=False,
            hovertemplate='<b>历史数据</b><br>' +
                         '时间: %{x|%m-%d %H:%M}<br>' +
                         '值: %{y:.5f}<br>' +
                         '<extra></extra>'
        ))

        # 添加异常值线段（紫色）
        if outlier_timestamps:
            # 将异常值时间戳转换为datetime对象
            outlier_datetimes = [datetime.fromtimestamp(ts) for ts in outlier_timestamps]

            # 为每个异常值创建单独的线段
            for outlier_dt in outlier_datetimes:
                if outlier_dt in df_original_history_smoothed.index:
                    outlier_value = df_original_history_smoothed[outlier_dt]
                    outlier_time_utc8 = convert_to_utc_plus_8([outlier_dt])[0]

                    # 找到前后相邻的时间点来创建线段
                    outlier_idx = df_original_history_smoothed.index.get_loc(outlier_dt)

                    # 创建包含前后点的小线段
                    segment_times = []
                    segment_values = []

                    # 添加前一个点（如果存在）
                    if outlier_idx > 0:
                        prev_time = df_original_history_smoothed.index[outlier_idx - 1]
                        prev_value = df_original_history_smoothed.iloc[outlier_idx - 1]
                        segment_times.append(convert_to_utc_plus_8([prev_time])[0])
                        segment_values.append(prev_value)

                    # 添加异常点
                    segment_times.append(outlier_time_utc8)
                    segment_values.append(outlier_value)

                    # 添加后一个点（如果存在）
                    if outlier_idx < len(df_original_history_smoothed) - 1:
                        next_time = df_original_history_smoothed.index[outlier_idx + 1]
                        next_value = df_original_history_smoothed.iloc[outlier_idx + 1]
                        segment_times.append(convert_to_utc_plus_8([next_time])[0])
                        segment_values.append(next_value)

                    # 添加紫色线段
                    fig_all.add_trace(go.Scatter(
                        x=segment_times,
                        y=segment_values,
                        mode='lines',
                        name="Outliers",
                        line=dict(color="#8A2BE2", width=2),  # 紫色
                        showlegend=False,
                        hovertemplate='<b>异常值</b><br>' +
                                     '时间: %{x|%m-%d %H:%M}<br>' +
                                     '值: %{y:.5f}<br>' +
                                     '<extra></extra>'
                    ))

        # 添加今日数据线（蓝色折线，使用平滑后的数据）
        fig_all.add_trace(go.Scatter(
            x=today_times_utc8_all,
            y=today_values_smoothed,
            mode='lines',
            name="Today",
            line=dict(color="#FF0000", width=3),
            showlegend=False,
            hovertemplate='<b>今日数据</b><br>' +
                         '时间: %{x|%m-%d %H:%M}<br>' +
                         '值: %{y:.5f}<br>' +
                         '<extra></extra>'
        ))

        fig_all.update_layout(
            margin=dict(l=0, r=0, t=0, b=50),  # 增加底部边距为rangeslider留空间
            showlegend=False,
            plot_bgcolor='white',
            paper_bgcolor='white',
            xaxis=dict(
                title=None,
                tickformat="%m-%d",
                showline=False,
                showgrid=False,
                zeroline=False,
                rangeslider=dict(
                    visible=True,
                    thickness=0.1,  # 控制滑块高度
                    bgcolor="rgba(240,240,240,0.8)",
                    bordercolor="rgba(200,200,200,0.8)",
                    borderwidth=1
                ),
                type="date"
            ),
            yaxis=dict(
                title=None,
                showline=False,
                showgrid=False,
                zeroline=False
            )
        )

        img_all_data = fig_all.to_html(
            include_plotlyjs='inline',
            config={'displayModeBar': False}
        )

    if debug_resid_image:
        # 3. 残差全量图
        fig_resid_data = go.Figure()

        # 转换时间为UTC+8用于显示
        history_times_utc8 = convert_to_utc_plus_8(df_history.index.tolist())
        today_times_utc8_resid = convert_to_utc_plus_8(today_times)

        # 添加历史残差线
        fig_resid_data.add_trace(go.Scatter(
            x=history_times_utc8,
            y=resid,
            mode='lines',
            name="History Residual",
            line=dict(color="#4A90E2", width=1),
            hovertemplate='<b>历史残差</b><br>' +
                         '时间: %{x|%m-%d %H:%M}<br>' +
                         '残差: %{y:.5f}<br>' +
                         '<extra></extra>'
        ))

        # 添加今日残差线
        fig_resid_data.add_trace(go.Scatter(
            x=today_times_utc8_resid,
            y=today_resid,
            mode='lines',
            name="Today Residual",
            line=dict(color="#FF0000", width=3),
            showlegend=False,
            hovertemplate='<b>今日残差</b><br>' +
                         '时间: %{x|%m-%d %H:%M}<br>' +
                         '残差: %{y:.5f}<br>' +
                         '<extra></extra>'
        ))

        fig_resid_data.update_layout(
            margin=dict(l=0, r=0, t=0, b=50),  # 增加底部边距为rangeslider留空间
            showlegend=False,
            plot_bgcolor='white',
            paper_bgcolor='white',
            xaxis=dict(
                title=None,
                tickformat="%m-%d",
                showline=False,
                showgrid=False,
                zeroline=False,
                rangeslider=dict(
                    visible=True,
                    thickness=0.1,  # 控制滑块高度
                    bgcolor="rgba(240,240,240,0.8)",
                    bordercolor="rgba(200,200,200,0.8)",
                    borderwidth=1
                ),
                type="date"
            ),
            yaxis=dict(
                title=None,
                showline=False,
                showgrid=False,
                zeroline=False
            )
        )

        img_resid_data = fig_resid_data.to_html(
            include_plotlyjs='inline',
            config={'displayModeBar': False}
        )

    if debug_other_image:
        # 转换时间为UTC+8用于其他图表显示
        history_times_utc8 = convert_to_utc_plus_8(df_history.index.tolist())
        today_times_utc8_other = convert_to_utc_plus_8(today_times)

        # 4. trend图
        fig_trend = go.Figure()

        # 添加历史趋势线
        fig_trend.add_trace(go.Scatter(
            x=history_times_utc8,
            y=trend,
            mode='lines',
            name="History Trend",
            line=dict(color="orange")
        ))

        # 添加预测趋势线
        fig_trend.add_trace(go.Scatter(
            x=today_times_utc8_other,
            y=trend_pred.values,
            mode='lines+markers',
            name="Pred Trend",
            line=dict(color="red", dash="dash"),
            marker=dict(color="red", size=6)
        ))

        fig_trend.update_layout(
            title="Trend: Prophet(History) + Prophet(Prediction)",
            xaxis_title="Time",
            yaxis_title="Trend",
            margin=dict(l=0, r=0, t=50, b=50),  # 增加底部边距为rangeslider留空间
            showlegend=True,
            xaxis=dict(
                rangeslider=dict(
                    visible=True,
                    thickness=0.1,  # 控制滑块高度
                    bgcolor="rgba(240,240,240,0.8)",
                    bordercolor="rgba(200,200,200,0.8)",
                    borderwidth=1
                ),
                type="date"
            )
        )

        img_trend = fig_trend.to_html(
            include_plotlyjs='inline',
            config={'displayModeBar': False}
        )

        # 5. daily图
        fig_daily = go.Figure()

        # 添加历史日周期性线
        fig_daily.add_trace(go.Scatter(
            x=history_times_utc8,
            y=daily,
            mode='lines',
            name="History Daily",
            line=dict(color="green")
        ))

        # 添加预测日周期性线
        fig_daily.add_trace(go.Scatter(
            x=today_times_utc8_other,
            y=daily_pred.values,
            mode='lines+markers',
            name="Pred Daily",
            line=dict(color="red", dash="dash"),
            marker=dict(color="red", size=6)
        ))

        fig_daily.update_layout(
            title="Daily: Prophet(History) + Prophet(Prediction)",
            xaxis_title="Time",
            yaxis_title="Daily",
            margin=dict(l=0, r=0, t=50, b=50),  # 增加底部边距为rangeslider留空间
            showlegend=True,
            xaxis=dict(
                rangeslider=dict(
                    visible=True,
                    thickness=0.1,  # 控制滑块高度
                    bgcolor="rgba(240,240,240,0.8)",
                    bordercolor="rgba(200,200,200,0.8)",
                    borderwidth=1
                ),
                type="date"
            )
        )

        img_daily = fig_daily.to_html(
            include_plotlyjs='inline',
            config={'displayModeBar': False}
        )



        # 6. 差分异常检测主图
        fig_diff_main = go.Figure()

        # 添加今日差分线
        fig_diff_main.add_trace(go.Scatter(
            x=today_times_utc8_other,
            y=today_diff,
            mode='lines+markers',
            name="Today Diff",
            line=dict(color="red"),
            hovertemplate='<b>今日差分</b><br>' +
                         '时间: %{x|%m-%d %H:%M}<br>' +
                         '差分值: %{y:.3f}<br>' +
                         '<extra></extra>'
        ))

        # 添加预测差分均值线
        fig_diff_main.add_trace(go.Scatter(
            x=today_times_utc8_other,
            y=diff_means,
            mode='lines',
            name="Predicted Diff Mean",
            line=dict(color="blue")
        ))

        # 添加差分σ范围
        fig_diff_main.add_trace(go.Scatter(
            x=today_times_utc8_other,
            y=smooth_diff_upper,
            mode='lines',
            line=dict(color="blue", width=0),
            showlegend=False
        ))
        fig_diff_main.add_trace(go.Scatter(
            x=today_times_utc8_other,
            y=smooth_diff_lower,
            mode='lines',
            line=dict(color="blue", width=0),
            fill='tonexty',
            fillcolor='rgba(0,0,255,0.2)',
            name=f"{sigma_diff} Sigma Diff Range"
        ))

        # 添加差分异常点
        if diff_anomaly_idx:
            fig_diff_main.add_trace(go.Scatter(
                x=[today_times_utc8_other[i] for i in diff_anomaly_idx],
                y=today_diff[diff_anomaly_idx],
                mode='markers',
                marker=dict(color="black", size=8),
                name="Diff Anomaly",
                hovertemplate='<b>差分异常点</b><br>' +
                             '时间: %{x|%m-%d %H:%M}<br>' +
                             '差分值: %{y:.3f}<br>' +
                             '<extra></extra>'
            ))

        fig_diff_main.update_layout(
            title=f"Diff Anomaly Detection ({sigma_diff}σ Range)",
            xaxis_title="Time",
            yaxis_title="Diff Residual",
            margin=dict(l=0, r=0, t=50, b=0),
            showlegend=True
        )

        img_diff_main = fig_diff_main.to_html(
            include_plotlyjs='inline',
            config={'displayModeBar': False}
        )

        # 7. 差分历史全量图
        fig_diff_all = go.Figure()

        # 计算历史差分数据（使用平滑后的数据）
        history_diff_times = []
        history_diff_values = []
        for t in range(1, len(df_history_smoothed)):
            history_diff_times.append(df_history_smoothed.index[t])
            history_diff_values.append(df_history_smoothed.iloc[t] - df_history_smoothed.iloc[t-1])

        # 转换历史差分时间为UTC+8
        history_diff_times_utc8 = convert_to_utc_plus_8(history_diff_times)

        # 添加历史差分线
        fig_diff_all.add_trace(go.Scatter(
            x=history_diff_times_utc8,
            y=history_diff_values,
            mode='lines',
            name="History Diff (All)",
            line=dict(color="gray"),
            opacity=0.7
        ))

        # 添加今日差分线
        fig_diff_all.add_trace(go.Scatter(
            x=today_times_utc8_other,
            y=today_diff,
            mode='lines',
            name="Today Diff",
            line=dict(color="red", width=2),
            hovertemplate='<b>今日差分</b><br>' +
                         '时间: %{x|%m-%d %H:%M}<br>' +
                         '差分值: %{y:.3f}<br>' +
                         '<extra></extra>'
        ))

        fig_diff_all.update_layout(
            title="Diff Residual (All) & Today",
            xaxis_title="Time",
            yaxis_title="Diff Residual",
            margin=dict(l=0, r=0, t=50, b=50),  # 增加底部边距为rangeslider留空间
            showlegend=True,
            xaxis=dict(
                rangeslider=dict(
                    visible=True,
                    thickness=0.1,  # 控制滑块高度
                    bgcolor="rgba(240,240,240,0.8)",
                    bordercolor="rgba(200,200,200,0.8)",
                    borderwidth=1
                ),
                type="date"
            )
        )

        img_diff_all = fig_diff_all.to_html(
            include_plotlyjs='inline',
            config={'displayModeBar': False}
        )
    
    draw_end_time = time.time()
    logging.info(f"绘制图表耗时: {draw_end_time - draw_start_time:.2f}秒")

    return (
        is_anomaly,
        anomaly_times,
        img_main,
        img_all_data,
        img_resid_data,
        {
            "trend": img_trend,
            "daily": img_daily,
            "diff_main": img_diff_main,
            "diff_all": img_diff_all,
            "noise": noise_filter_plot if debug_other_image else "",
        },
    )