# Anomaly Detection Service

A time series anomaly detection service using Prophet algorithm.

## Features

- Prophet-based time series decomposition and forecasting
- Parallel data querying for improved performance
- Historical data validation (requires at least 2 days of historical data)
- Multi-dimensional metric analysis
- Comprehensive anomaly detection with statistical thresholds

## API Endpoints

### `/anomaly_detection`

**Method:** POST

**Parameters:**
- `start_time`: Start timestamp for detection period
- `end_time`: End timestamp for detection period  
- `vregion`: Virtual region identifier
- `query`: Metric query string
- `block_cache`: Boolean flag for cache blocking (optional)

**Response:**
```json
{
  "code": 0,
  "results": [
    {
      "dimensions": {...},
      "status": "success|insufficient_data",
      "message": "Error message if applicable",
      "is_anomaly": true|false,
      "anomaly_times": [...],
      "img_base64": "...",
      "img_details": {...}
    }
  ]
}
```

## Status Codes

- `success`: Normal anomaly detection completed
- `insufficient_data`: Historical data is insufficient (less than 2 days)

## Dependencies

- Flask
- Prophet
- pandas
- numpy
- matplotlib
- requests

## Environment

Supports both production and development environments with timezone handling (UTC).
