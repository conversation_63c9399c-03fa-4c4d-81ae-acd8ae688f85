#!/usr/bin/env python3
"""
测试最终模块化后的Prophet异常检测函数
"""

import sys
import os
import time
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from component.prophet_detection import prophet_anomaly_detection

def create_test_data():
    """创建测试用的历史数据和今日数据"""
    base_time = int(time.time()) - 7 * 24 * 3600  # 7天前开始
    
    # 创建历史数据（7天）
    history_data = []
    for i in range(200):  # 200个数据点
        timestamp = base_time + i * 2400  # 每40分钟一个数据点
        # 基础值：正弦波 + 噪声
        value = 100 + 8 * np.sin(i * 0.08) + np.random.normal(0, 1.5)
        
        # 添加一些异常值
        if i in [40, 41]:  # 连续异常值
            value += 20  # 异常高值
        elif i in [100]:  # 单个异常值
            value -= 15  # 异常低值
        elif i in [160]:  # 单个异常值
            value += 18  # 异常高值
            
        history_data.append({"timestamp": timestamp, "value": value})
    
    # 创建今日数据（1天）
    today_start_time = base_time + 200 * 2400
    today_data = []
    for i in range(36):  # 36个数据点（1天，每40分钟一个点）
        timestamp = today_start_time + i * 2400
        # 基础值：正弦波 + 噪声
        value = 100 + 8 * np.sin((200 + i) * 0.08) + np.random.normal(0, 1.5)
        
        # 添加一些今日异常值
        if i in [15, 16]:  # 连续异常值
            value += 25  # 异常高值
        elif i in [25]:  # 单个异常值
            value -= 20  # 异常低值
            
        today_data.append({"timestamp": timestamp, "value": value})
    
    return history_data, today_data

def test_final_modularized_prophet():
    """测试最终模块化后的Prophet异常检测函数"""
    print("创建测试数据...")
    history_data, today_data = create_test_data()
    print(f"历史数据点数: {len(history_data)}")
    print(f"今日数据点数: {len(today_data)}")
    
    print("\n开始测试最终模块化的Prophet异常检测...")
    start_time = datetime.now()
    
    # 测试异常检测函数（启用所有调试图表）
    is_anomaly, anomaly_times, img_main, img_all_data, img_resid_data, debug_other_images = prophet_anomaly_detection(
        all_history=history_data,
        today=today_data,
        start_time=start_time,
        debug_main_image=True,
        debug_all_image=True,
        debug_other_image=True,
        debug_resid_image=True,
        min_anomaly_run=4,
        sigma_soft=3,
        sigma_hard=5,
        sigma_diff=5
    )
    
    print(f"异常检测完成:")
    print(f"- 是否有异常: {is_anomaly}")
    print(f"- 异常时间点数量: {len(anomaly_times)}")
    if anomaly_times:
        print(f"- 异常时间点: {[t.strftime('%H:%M:%S') for t in anomaly_times[:5]]}...")  # 只显示前5个
    
    # 检查各个图表
    print(f"\n图表生成情况:")
    print(f"- 主图长度: {len(img_main)} 字符")
    print(f"- 历史全量图长度: {len(img_all_data)} 字符")
    print(f"- 残差图长度: {len(img_resid_data)} 字符")
    
    # 检查其他调试图表
    for img_name, img_content in debug_other_images.items():
        print(f"- {img_name}图表长度: {len(img_content)} 字符")
    
    # 保存主要图表
    if img_main:
        with open("final_test_main.html", "w", encoding="utf-8") as f:
            f.write(img_main)
        print("主图已保存为 final_test_main.html")
    
    if img_all_data:
        with open("final_test_all_data.html", "w", encoding="utf-8") as f:
            f.write(img_all_data)
        print("历史全量图已保存为 final_test_all_data.html")
    
    if debug_other_images.get("noise"):
        with open("final_test_noise.html", "w", encoding="utf-8") as f:
            f.write(debug_other_images["noise"])
        print("噪声过滤图已保存为 final_test_noise.html")
    
    return True

if __name__ == "__main__":
    try:
        success = test_final_modularized_prophet()
        if success:
            print("\n✅ 最终模块化Prophet异常检测函数测试通过！")
            print("模块化结构:")
            print("- 模块1: 训练和分解部分（包括历史数据分量获取）")
            print("- 模块2: 阈值计算和异常检测部分")
            print("- 模块3: 绘图部分（主逻辑在主函数中，子图函数独立）")
        else:
            print("\n❌ 测试失败")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
