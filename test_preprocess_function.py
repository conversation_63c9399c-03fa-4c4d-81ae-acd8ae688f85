#!/usr/bin/env python3
"""
测试新的历史数据预处理函数
"""

import sys
import os
import time
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from component.prophet_detection import preprocess_history_data

def create_test_history_data():
    """创建测试用的历史数据"""
    base_time = int(time.time()) - 7 * 24 * 3600  # 7天前开始
    test_data = []
    
    for i in range(500):  # 500个数据点
        timestamp = base_time + i * 600  # 每10分钟一个数据点
        # 基础值：正弦波 + 噪声
        value = 100 + 15 * np.sin(i * 0.05) + np.random.normal(0, 2)
        
        # 添加一些异常值
        if i in [100, 101, 102]:  # 连续异常值
            value += 30  # 异常高值
        elif i in [200]:  # 单个异常值
            value -= 25  # 异常低值
        elif i in [350, 351]:  # 两个连续异常值
            value += 25  # 异常高值
            
        test_data.append({"timestamp": timestamp, "value": value})
    
    return test_data

def test_preprocess_function():
    """测试预处理函数"""
    print("创建测试历史数据...")
    history_data = create_test_history_data()
    print(f"原始历史数据点数: {len(history_data)}")
    
    print("\n开始测试预处理函数...")
    
    # 测试预处理函数
    processed_data, outlier_indices, noise_filter_plot = preprocess_history_data(
        history_data,
        noise_filter_method="iqr",
        noise_threshold=2.5,
        smoothing_window=3,
        smoothing_method="mean",
        create_noise_plot=True
    )
    
    print(f"预处理完成:")
    print(f"- 处理后数据点数: {len(processed_data)}")
    print(f"- 异常值数量: {len(outlier_indices)}")
    print(f"- 异常值时间戳: {outlier_indices[:5]}...")  # 只显示前5个
    print(f"- 噪声过滤图表HTML长度: {len(noise_filter_plot) if noise_filter_plot else 0} 字符")
    
    # 验证数据类型
    print(f"\n数据类型验证:")
    print(f"- processed_data类型: {type(processed_data)}")
    print(f"- processed_data索引类型: {type(processed_data.index[0])}")
    print(f"- outlier_indices类型: {type(outlier_indices)}")
    print(f"- noise_filter_plot类型: {type(noise_filter_plot)}")
    
    # 保存噪声过滤图表
    if noise_filter_plot:
        with open("preprocess_test_noise_filter.html", "w", encoding="utf-8") as f:
            f.write(noise_filter_plot)
        print("噪声过滤图表已保存为 preprocess_test_noise_filter.html")
    
    # 验证时间范围
    if len(processed_data) > 0:
        start_time = processed_data.index[0]
        end_time = processed_data.index[-1]
        print(f"\n处理后数据时间范围:")
        print(f"- 开始时间: {start_time}")
        print(f"- 结束时间: {end_time}")
        print(f"- 时间跨度: {end_time - start_time}")
    
    return True

if __name__ == "__main__":
    try:
        success = test_preprocess_function()
        if success:
            print("\n✅ 预处理函数测试通过！")
        else:
            print("\n❌ 测试失败")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
