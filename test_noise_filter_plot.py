#!/usr/bin/env python3
"""
测试噪声过滤绘图功能的简单脚本
"""

import sys
import os
import time
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from component.data_preprocessing import DataPreprocessor

def create_test_data_with_outliers():
    """创建包含明显异常值的测试数据"""
    base_time = int(time.time()) - 24 * 3600  # 1天前开始
    test_data = []
    
    for i in range(200):  # 200个数据点
        timestamp = base_time + i * 300  # 每5分钟一个数据点
        # 基础值：正弦波 + 小噪声
        value = 100 + 10 * np.sin(i * 0.1) + np.random.normal(0, 1)
        
        # 添加明显的异常值
        if i in [50, 100, 150]:
            value += 30  # 异常高值
        elif i in [75, 125, 175]:
            value -= 25  # 异常低值
            
        test_data.append({"timestamp": timestamp, "value": value})
    
    return test_data

def test_noise_filter_with_plot():
    """测试带绘图功能的噪声过滤"""
    print("创建测试数据...")
    test_data = create_test_data_with_outliers()
    print(f"原始数据点数: {len(test_data)}")
    
    print("\n测试噪声过滤绘图功能...")
    preprocessor = DataPreprocessor()
    
    # 测试带绘图的噪声过滤
    filtered_data, outlier_timestamps, plot_html = preprocessor.filter_noise(
        test_data, 
        method="zscore", 
        threshold=2.5,  # 使用较低的阈值以便检测到异常值
        create_plot=True
    )
    
    print(f"过滤后数据点数: {len(filtered_data)}")
    print(f"检测到的异常点数: {len(outlier_timestamps)}")
    print(f"异常点时间戳: {outlier_timestamps}")
    
    if plot_html:
        print(f"生成的图表HTML长度: {len(plot_html)} 字符")
        
        # 保存HTML文件以便查看
        with open("noise_filter_test.html", "w", encoding="utf-8") as f:
            f.write(plot_html)
        print("图表已保存为 noise_filter_test.html")
        
        return True
    else:
        print("❌ 未生成图表HTML")
        return False

if __name__ == "__main__":
    try:
        success = test_noise_filter_with_plot()
        if success:
            print("\n✅ 噪声过滤绘图功能测试通过！")
        else:
            print("\n❌ 测试失败")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
